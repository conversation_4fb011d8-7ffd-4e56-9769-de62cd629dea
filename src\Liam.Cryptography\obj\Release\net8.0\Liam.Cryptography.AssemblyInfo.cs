//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("Liam")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Release")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(("Liam系列加密功能库，提供完整的对称加密、非对称加密、哈希算法、数字签名和密钥管理功能。支持AES、ChaCha20-Poly1305、RSA、ECDSA、SH" +
    "A256、Argon2等现代加密算法，包含流式处理和性能优化。"))]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.1.0+8e3fbb372e0dd611900e80bc3845a91692bd2f0e")]
[assembly: System.Reflection.AssemblyProductAttribute("Liam.Cryptography")]
[assembly: System.Reflection.AssemblyTitleAttribute("Liam.Cryptography")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://gitee.com/liam-gitee/liam.git")]

// 由 MSBuild WriteCodeFragment 类生成。

