﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Liam.SerialPort</id>
    <version>1.0.0</version>
    <authors><PERSON></authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>icon.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://gitee.com/liam-gitee/liam</projectUrl>
    <description>现代化串口通讯功能库，支持跨平台串口设备发现、连接管理、数据收发、热插拔检测和自动重连等功能</description>
    <tags>serialport communication cross-platform dotnet8 async hotplug reconnect</tags>
    <repository type="git" url="https://gitee.com/liam-gitee/liam" commit="8e3fbb372e0dd611900e80bc3845a91692bd2f0e" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.1" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging.Abstractions" version="8.0.1" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Options" version="8.0.2" exclude="Build,Analyzers" />
        <dependency id="System.IO.Ports" version="8.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\Project\00 Liam\src\Liam.SerialPort\bin\Release\net8.0\Liam.SerialPort.dll" target="lib\net8.0\Liam.SerialPort.dll" />
    <file src="D:\Project\00 Liam\src\Liam.SerialPort\bin\Release\net8.0\Liam.SerialPort.xml" target="lib\net8.0\Liam.SerialPort.xml" />
    <file src="D:\Project\00 Liam\icon.png" target="\icon.png" />
    <file src="D:\Project\00 Liam\src\Liam.SerialPort\README.md" target="\README.md" />
  </files>
</package>