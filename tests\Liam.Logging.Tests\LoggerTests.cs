using FluentAssertions;
using Liam.Logging.Constants;
using Liam.Logging.Interfaces;
using Liam.Logging.Models;
using Liam.Logging.Services;
using Moq;
using Xunit;

namespace Liam.Logging.Tests;

/// <summary>
/// 日志记录器测试
/// </summary>
public class LoggerTests
{
    private readonly Mock<ILiamLoggerFactory> _mockFactory;
    private readonly Mock<ILogScopeProvider> _mockScopeProvider;
    private readonly Mock<ILogProvider> _mockProvider;
    private readonly LiamLogger _logger;

    public LoggerTests()
    {
        _mockFactory = new Mock<ILiamLoggerFactory>();
        _mockScopeProvider = new Mock<ILogScopeProvider>();
        _mockProvider = new Mock<ILogProvider>();

        _mockScopeProvider.Setup(x => x.GetCurrentScopes())
            .Returns(new Dictionary<string, object?>());

        _mockFactory.Setup(x => x.GetProviders())
            .Returns(new[] { _mockProvider.Object });

        _mockProvider.Setup(x => x.IsEnabled).Returns(true);

        _logger = new LiamLogger("TestCategory", _mockFactory.Object, _mockScopeProvider.Object);
    }

    [Fact]
    public void Constructor_ShouldSetCategoryName()
    {
        // Arrange & Act & Assert
        _logger.CategoryName.Should().Be("TestCategory");
    }

    [Fact]
    public void Constructor_WithNullCategoryName_ShouldThrowArgumentNullException()
    {
        // Arrange & Act & Assert
        var act = () => new LiamLogger(null!, _mockFactory.Object, _mockScopeProvider.Object);
        act.Should().Throw<ArgumentNullException>();
    }

    [Theory]
    [InlineData(LogLevel.Trace, LogLevel.Information, false)]
    [InlineData(LogLevel.Debug, LogLevel.Information, false)]
    [InlineData(LogLevel.Information, LogLevel.Information, true)]
    [InlineData(LogLevel.Warning, LogLevel.Information, true)]
    [InlineData(LogLevel.Error, LogLevel.Information, true)]
    [InlineData(LogLevel.Critical, LogLevel.Information, true)]
    public void IsEnabled_ShouldReturnCorrectValue(LogLevel level, LogLevel minimumLevel, bool expected)
    {
        // Arrange
        _logger.MinimumLevel = minimumLevel;

        // Act
        var result = _logger.IsEnabled(level);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void Log_WithEnabledLevel_ShouldCallProvider()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Information;
        var message = "Test message";

        // Act
        _logger.Log(LogLevel.Information, message);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(It.Is<LogEvent>(e => 
            e.Level == LogLevel.Information && 
            e.Message == message && 
            e.Category == "TestCategory")), Times.Once);
    }

    [Fact]
    public void Log_WithDisabledLevel_ShouldNotCallProvider()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Information;
        var message = "Test message";

        // Act
        _logger.Log(LogLevel.Debug, message);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(It.IsAny<LogEvent>()), Times.Never);
    }

    [Fact]
    public void LogStructured_ShouldFormatMessageAndCallProvider()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Information;
        var messageTemplate = "User {UserId} performed action {Action}";
        var parameters = new object[] { 123, "Login" };

        // Act
        _logger.LogStructured(LogLevel.Information, messageTemplate, parameters);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(It.Is<LogEvent>(e => 
            e.Level == LogLevel.Information && 
            e.MessageTemplate == messageTemplate &&
            e.Parameters == parameters)), Times.Once);
    }

    [Fact]
    public void LogEvent_ShouldCallProvider()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Information;
        var logEvent = new LogEvent
        {
            Level = LogLevel.Information,
            Message = "Test message",
            Category = "TestCategory"
        };

        // Act
        _logger.LogEvent(logEvent);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(logEvent), Times.Once);
    }

    [Fact]
    public async Task LogAsync_ShouldCallProviderAsync()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Information;
        var message = "Test async message";

        // Act
        await _logger.LogAsync(LogLevel.Information, message);

        // Assert
        _mockProvider.Verify(x => x.WriteLogAsync(It.Is<LogEvent>(e => 
            e.Level == LogLevel.Information && 
            e.Message == message), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void BeginScope_ShouldCallScopeProvider()
    {
        // Arrange
        var scopeState = "Test scope";
        var mockDisposable = new Mock<IDisposable>();
        _mockScopeProvider.Setup(x => x.Push(scopeState)).Returns(mockDisposable.Object);

        // Act
        var result = _logger.BeginScope(scopeState);

        // Assert
        result.Should().Be(mockDisposable.Object);
        _mockScopeProvider.Verify(x => x.Push(scopeState), Times.Once);
    }

    [Fact]
    public void LogTrace_ShouldLogWithTraceLevel()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Trace;
        var message = "Trace message";

        // Act
        _logger.LogTrace(message);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(It.Is<LogEvent>(e => 
            e.Level == LogLevel.Trace && 
            e.Message == message)), Times.Once);
    }

    [Fact]
    public void LogDebug_ShouldLogWithDebugLevel()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Debug;
        var message = "Debug message";

        // Act
        _logger.LogDebug(message);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(It.Is<LogEvent>(e => 
            e.Level == LogLevel.Debug && 
            e.Message == message)), Times.Once);
    }

    [Fact]
    public void LogInformation_ShouldLogWithInformationLevel()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Information;
        var message = "Information message";

        // Act
        _logger.LogInformation(message);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(It.Is<LogEvent>(e => 
            e.Level == LogLevel.Information && 
            e.Message == message)), Times.Once);
    }

    [Fact]
    public void LogWarning_ShouldLogWithWarningLevel()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Warning;
        var message = "Warning message";
        var exception = new Exception("Test exception");

        // Act
        _logger.LogWarning(message, exception);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(It.Is<LogEvent>(e => 
            e.Level == LogLevel.Warning && 
            e.Message == message &&
            e.Exception == exception)), Times.Once);
    }

    [Fact]
    public void LogError_ShouldLogWithErrorLevel()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Error;
        var message = "Error message";
        var exception = new Exception("Test exception");

        // Act
        _logger.LogError(message, exception);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(It.Is<LogEvent>(e => 
            e.Level == LogLevel.Error && 
            e.Message == message &&
            e.Exception == exception)), Times.Once);
    }

    [Fact]
    public void LogCritical_ShouldLogWithCriticalLevel()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Critical;
        var message = "Critical message";
        var exception = new Exception("Test exception");

        // Act
        _logger.LogCritical(message, exception);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(It.Is<LogEvent>(e => 
            e.Level == LogLevel.Critical && 
            e.Message == message &&
            e.Exception == exception)), Times.Once);
    }

    [Fact]
    public void Log_WithException_ShouldIncludeExceptionInLogEvent()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Error;
        var message = "Error occurred";
        var exception = new InvalidOperationException("Test exception");

        // Act
        _logger.Log(LogLevel.Error, message, exception);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(It.Is<LogEvent>(e => 
            e.Exception == exception)), Times.Once);
    }

    [Fact]
    public void Log_WithSourceInfo_ShouldIncludeSourceInLogEvent()
    {
        // Arrange
        _logger.MinimumLevel = LogLevel.Information;
        var message = "Test message";

        // Act
        _logger.Log(LogLevel.Information, message, null, "TestMethod", "TestFile.cs", 42);

        // Assert
        _mockProvider.Verify(x => x.WriteLog(It.Is<LogEvent>(e => 
            e.Source != null &&
            e.Source.MethodName == "TestMethod" &&
            e.Source.FilePath == "TestFile.cs" &&
            e.Source.LineNumber == 42)), Times.Once);
    }
}

/// <summary>
/// 泛型日志记录器测试
/// </summary>
public class GenericLoggerTests
{
    [Fact]
    public void Constructor_ShouldSetCategoryNameFromType()
    {
        // Arrange
        var mockFactory = new Mock<ILiamLoggerFactory>();
        var mockScopeProvider = new Mock<ILogScopeProvider>();

        mockScopeProvider.Setup(x => x.GetCurrentScopes())
            .Returns(new Dictionary<string, object?>());

        // Act
        var logger = new LiamLogger<GenericLoggerTests>(mockFactory.Object, mockScopeProvider.Object);

        // Assert
        logger.CategoryName.Should().Be(typeof(GenericLoggerTests).FullName);
    }
}
