﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Liam.Logging</id>
    <version>1.0.0</version>
    <authors><PERSON></authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>icon.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://gitee.com/liam-gitee/liam</projectUrl>
    <description>现代化日志记录功能库，支持多级日志、多种输出目标、结构化日志、异步记录、日志轮转等功能，完全兼容Microsoft.Extensions.Logging</description>
    <tags>logging logger structured-logging async-logging file-logging console-logging database-logging log-rotation performance dotnet8</tags>
    <repository type="git" url="https://gitee.com/liam-gitee/liam" commit="8e3fbb372e0dd611900e80bc3845a91692bd2f0e" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="Microsoft.Extensions.Configuration.Abstractions" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Configuration.Binder" version="8.0.2" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.1" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Hosting.Abstractions" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging.Abstractions" version="8.0.1" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Options" version="8.0.2" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="8.0.5" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\Project\00 Liam\src\Liam.Logging\bin\Debug\net8.0\Liam.Logging.dll" target="lib\net8.0\Liam.Logging.dll" />
    <file src="D:\Project\00 Liam\src\Liam.Logging\bin\Debug\net8.0\Liam.Logging.xml" target="lib\net8.0\Liam.Logging.xml" />
    <file src="D:\Project\00 Liam\icon.png" target="\icon.png" />
    <file src="D:\Project\00 Liam\src\Liam.Logging\README.md" target="\README.md" />
  </files>
</package>