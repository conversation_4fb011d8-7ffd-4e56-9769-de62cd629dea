﻿<?xml version="1.0" encoding="utf-8"?>
<coverage line-rate="0.1979" branch-rate="0.2173" version="1.9" timestamp="1749956582" lines-covered="375" lines-valid="1894" branches-covered="123" branches-valid="566">
  <sources>
    <source>D:\Project\00 Liam\src\Liam.SerialPort\</source>
  </sources>
  <packages>
    <package name="Liam.SerialPort" line-rate="0.1979" branch-rate="0.2173" complexity="740">
      <classes>
        <class name="Liam.SerialPort.Services.SerialPortConnection" filename="Services\SerialPortConnection.cs" line-rate="0" branch-rate="0" complexity="73">
          <methods>
            <method name="get_Status" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="32" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Settings" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="37" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="7" type="jump" coverage="0%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name="get_PortInfo" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="42" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="7" type="jump" coverage="0%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name="get_IsConnected" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="47" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="7" type="jump" coverage="0%" />
                    <condition number="16" type="jump" coverage="0%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name="get_ConnectedAt" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="52" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_LastActivity" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="57" hits="0" branch="False" />
              </lines>
            </method>
            <method name="GetStatistics" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="329" hits="0" branch="False" />
                <line number="330" hits="0" branch="False" />
                <line number="331" hits="0" branch="False" />
                <line number="332" hits="0" branch="False" />
                <line number="333" hits="0" branch="False" />
                <line number="334" hits="0" branch="False" />
                <line number="335" hits="0" branch="False" />
                <line number="336" hits="0" branch="False" />
                <line number="337" hits="0" branch="False" />
                <line number="338" hits="0" branch="False" />
                <line number="339" hits="0" branch="False" />
                <line number="340" hits="0" branch="False" />
                <line number="341" hits="0" branch="False" />
                <line number="342" hits="0" branch="False" />
                <line number="343" hits="0" branch="False" />
                <line number="344" hits="0" branch="False" />
              </lines>
            </method>
            <method name="GetSerialPort" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="351" hits="0" branch="False" />
                <line number="352" hits="0" branch="False" />
                <line number="353" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ChangeStatus" signature="(Liam.SerialPort.Models.ConnectionStatus,System.String)" line-rate="0" branch-rate="0" complexity="6">
              <lines>
                <line number="361" hits="0" branch="False" />
                <line number="362" hits="0" branch="False" />
                <line number="363" hits="0" branch="False" />
                <line number="365" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="24" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="366" hits="0" branch="False" />
                <line number="367" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="35" type="jump" coverage="0%" />
                    <condition number="47" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="368" hits="0" branch="False" />
                <line number="369" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnStatusChanged" signature="(Liam.SerialPort.Events.ConnectionStatusChangedEventArgs)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="376" hits="0" branch="False" />
                <line number="378" hits="0" branch="False" />
                <line number="379" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="380" hits="0" branch="False" />
                <line number="381" hits="0" branch="False" />
                <line number="382" hits="0" branch="False" />
                <line number="383" hits="0" branch="False" />
                <line number="384" hits="0" branch="False" />
                <line number="385" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnErrorOccurred" signature="(Liam.SerialPort.Events.SerialPortErrorEventArgs)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="392" hits="0" branch="False" />
                <line number="394" hits="0" branch="False" />
                <line number="395" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="396" hits="0" branch="False" />
                <line number="397" hits="0" branch="False" />
                <line number="398" hits="0" branch="False" />
                <line number="399" hits="0" branch="False" />
                <line number="400" hits="0" branch="False" />
                <line number="401" hits="0" branch="False" />
              </lines>
            </method>
            <method name="StartAutoReconnect" signature="()" line-rate="0" branch-rate="0" complexity="14">
              <lines>
                <line number="407" hits="0" branch="False" />
                <line number="408" hits="0" branch="True" condition-coverage="0% (0/6)">
                  <conditions>
                    <condition number="8" type="jump" coverage="0%" />
                    <condition number="22" type="jump" coverage="0%" />
                    <condition number="35" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="409" hits="0" branch="False" />
                <line number="411" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="54" type="jump" coverage="0%" />
                    <condition number="83" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="412" hits="0" branch="False" />
                <line number="413" hits="0" branch="False" />
                <line number="414" hits="0" branch="False" />
                <line number="417" hits="0" branch="False" />
                <line number="418" hits="0" branch="False" />
                <line number="420" hits="0" branch="False" />
                <line number="422" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="222" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="423" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="254" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="424" hits="0" branch="False" />
                <line number="425" hits="0" branch="False" />
                <line number="426" hits="0" branch="False" />
                <line number="427" hits="0" branch="False" />
                <line number="428" hits="0" branch="False" />
                <line number="429" hits="0" branch="False" />
                <line number="430" hits="0" branch="False" />
                <line number="431" hits="0" branch="False" />
                <line number="432" hits="0" branch="False" />
                <line number="433" hits="0" branch="False" />
                <line number="434" hits="0" branch="False" />
                <line number="435" hits="0" branch="False" />
                <line number="436" hits="0" branch="False" />
                <line number="437" hits="0" branch="False" />
                <line number="438" hits="0" branch="False" />
                <line number="439" hits="0" branch="False" />
                <line number="440" hits="0" branch="False" />
                <line number="441" hits="0" branch="False" />
              </lines>
            </method>
            <method name="StartHeartbeat" signature="()" line-rate="0" branch-rate="0" complexity="12">
              <lines>
                <line number="447" hits="0" branch="False" />
                <line number="448" hits="0" branch="True" condition-coverage="0% (0/6)">
                  <conditions>
                    <condition number="8" type="jump" coverage="0%" />
                    <condition number="25" type="jump" coverage="0%" />
                    <condition number="38" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="449" hits="0" branch="False" />
                <line number="451" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="49" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="452" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="81" type="jump" coverage="0%" />
                    <condition number="103" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="453" hits="0" branch="False" />
                <line number="454" hits="0" branch="False" />
                <line number="455" hits="0" branch="False" />
                <line number="456" hits="0" branch="False" />
                <line number="457" hits="0" branch="False" />
                <line number="458" hits="0" branch="False" />
                <line number="459" hits="0" branch="False" />
                <line number="460" hits="0" branch="False" />
                <line number="461" hits="0" branch="False" />
                <line number="462" hits="0" branch="False" />
                <line number="463" hits="0" branch="False" />
                <line number="464" hits="0" branch="False" />
                <line number="465" hits="0" branch="False" />
                <line number="466" hits="0" branch="False" />
                <line number="467" hits="0" branch="False" />
                <line number="468" hits="0" branch="False" />
                <line number="469" hits="0" branch="False" />
                <line number="470" hits="0" branch="False" />
                <line number="471" hits="0" branch="False" />
                <line number="472" hits="0" branch="False" />
                <line number="473" hits="0" branch="False" />
                <line number="474" hits="0" branch="False" />
                <line number="475" hits="0" branch="False" />
                <line number="476" hits="0" branch="False" />
                <line number="477" hits="0" branch="False" />
                <line number="478" hits="0" branch="False" />
                <line number="479" hits="0" branch="False" />
                <line number="480" hits="0" branch="False" />
                <line number="481" hits="0" branch="False" />
                <line number="482" hits="0" branch="False" />
                <line number="483" hits="0" branch="False" />
                <line number="484" hits="0" branch="False" />
                <line number="485" hits="0" branch="False" />
                <line number="486" hits="0" branch="False" />
              </lines>
            </method>
            <method name="StopTimers" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="492" hits="0" branch="False" />
                <line number="493" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="8" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="494" hits="0" branch="False" />
                <line number="496" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="33" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="497" hits="0" branch="False" />
                <line number="498" hits="0" branch="False" />
              </lines>
            </method>
            <method name="CleanupSerialPort" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="504" hits="0" branch="False" />
                <line number="506" hits="0" branch="False" />
                <line number="507" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="13" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="508" hits="0" branch="False" />
                <line number="509" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="29" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="510" hits="0" branch="False" />
                <line number="511" hits="0" branch="False" />
                <line number="512" hits="0" branch="False" />
                <line number="513" hits="0" branch="False" />
                <line number="514" hits="0" branch="False" />
                <line number="515" hits="0" branch="False" />
                <line number="516" hits="0" branch="False" />
                <line number="517" hits="0" branch="False" />
                <line number="518" hits="0" branch="False" />
                <line number="519" hits="0" branch="False" />
                <line number="520" hits="0" branch="False" />
                <line number="521" hits="0" branch="False" />
              </lines>
            </method>
            <method name="Dispose" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="527" hits="0" branch="False" />
                <line number="528" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="529" hits="0" branch="False" />
                <line number="532" hits="0" branch="False" />
                <line number="533" hits="0" branch="False" />
                <line number="534" hits="0" branch="False" />
                <line number="535" hits="0" branch="False" />
                <line number="536" hits="0" branch="False" />
                <line number="537" hits="0" branch="False" />
                <line number="538" hits="0" branch="False" />
                <line number="539" hits="0" branch="False" />
                <line number="541" hits="0" branch="False" />
                <line number="542" hits="0" branch="False" />
                <line number="543" hits="0" branch="False" />
                <line number="544" hits="0" branch="False" />
              </lines>
            </method>
            <method name="&lt;ConnectAsync&gt;b__31_0" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="157" hits="0" branch="False" />
                <line number="158" hits="0" branch="False" />
                <line number="159" hits="0" branch="False" />
                <line number="160" hits="0" branch="False" />
              </lines>
            </method>
            <method name="&lt;DisconnectAsync&gt;b__32_0" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="249" hits="0" branch="False" />
                <line number="250" hits="0" branch="False" />
                <line number="251" hits="0" branch="False" />
              </lines>
            </method>
            <method name="&lt;TestConnectionAsync&gt;b__34_0" signature="()" line-rate="0" branch-rate="0" complexity="8">
              <lines>
                <line number="307" hits="0" branch="False" />
                <line number="308" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="8" type="jump" coverage="0%" />
                    <condition number="24" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="309" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="33" type="jump" coverage="0%" />
                    <condition number="45" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="312" hits="0" branch="False" />
                <line number="313" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(Microsoft.Extensions.Logging.ILogger`1&lt;Liam.SerialPort.Services.SerialPortConnection&gt;)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="17" hits="0" branch="False" />
                <line number="18" hits="0" branch="False" />
                <line number="21" hits="0" branch="False" />
                <line number="73" hits="0" branch="False" />
                <line number="74" hits="0" branch="False" />
                <line number="75" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="40" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="76" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="32" hits="0" branch="False" />
            <line number="37" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="7" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="42" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="7" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="47" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="7" type="jump" coverage="0%" />
                <condition number="16" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="52" hits="0" branch="False" />
            <line number="57" hits="0" branch="False" />
            <line number="329" hits="0" branch="False" />
            <line number="330" hits="0" branch="False" />
            <line number="331" hits="0" branch="False" />
            <line number="332" hits="0" branch="False" />
            <line number="333" hits="0" branch="False" />
            <line number="334" hits="0" branch="False" />
            <line number="335" hits="0" branch="False" />
            <line number="336" hits="0" branch="False" />
            <line number="337" hits="0" branch="False" />
            <line number="338" hits="0" branch="False" />
            <line number="339" hits="0" branch="False" />
            <line number="340" hits="0" branch="False" />
            <line number="341" hits="0" branch="False" />
            <line number="342" hits="0" branch="False" />
            <line number="343" hits="0" branch="False" />
            <line number="344" hits="0" branch="False" />
            <line number="351" hits="0" branch="False" />
            <line number="352" hits="0" branch="False" />
            <line number="353" hits="0" branch="False" />
            <line number="361" hits="0" branch="False" />
            <line number="362" hits="0" branch="False" />
            <line number="363" hits="0" branch="False" />
            <line number="365" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="24" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="366" hits="0" branch="False" />
            <line number="367" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="35" type="jump" coverage="0%" />
                <condition number="47" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="368" hits="0" branch="False" />
            <line number="369" hits="0" branch="False" />
            <line number="376" hits="0" branch="False" />
            <line number="378" hits="0" branch="False" />
            <line number="379" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="380" hits="0" branch="False" />
            <line number="381" hits="0" branch="False" />
            <line number="382" hits="0" branch="False" />
            <line number="383" hits="0" branch="False" />
            <line number="384" hits="0" branch="False" />
            <line number="385" hits="0" branch="False" />
            <line number="392" hits="0" branch="False" />
            <line number="394" hits="0" branch="False" />
            <line number="395" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="396" hits="0" branch="False" />
            <line number="397" hits="0" branch="False" />
            <line number="398" hits="0" branch="False" />
            <line number="399" hits="0" branch="False" />
            <line number="400" hits="0" branch="False" />
            <line number="401" hits="0" branch="False" />
            <line number="407" hits="0" branch="False" />
            <line number="408" hits="0" branch="True" condition-coverage="0% (0/6)">
              <conditions>
                <condition number="8" type="jump" coverage="0%" />
                <condition number="22" type="jump" coverage="0%" />
                <condition number="35" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="409" hits="0" branch="False" />
            <line number="411" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="54" type="jump" coverage="0%" />
                <condition number="83" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="412" hits="0" branch="False" />
            <line number="413" hits="0" branch="False" />
            <line number="414" hits="0" branch="False" />
            <line number="417" hits="0" branch="False" />
            <line number="418" hits="0" branch="False" />
            <line number="420" hits="0" branch="False" />
            <line number="422" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="222" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="423" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="254" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="424" hits="0" branch="False" />
            <line number="425" hits="0" branch="False" />
            <line number="426" hits="0" branch="False" />
            <line number="427" hits="0" branch="False" />
            <line number="428" hits="0" branch="False" />
            <line number="429" hits="0" branch="False" />
            <line number="430" hits="0" branch="False" />
            <line number="431" hits="0" branch="False" />
            <line number="432" hits="0" branch="False" />
            <line number="433" hits="0" branch="False" />
            <line number="434" hits="0" branch="False" />
            <line number="435" hits="0" branch="False" />
            <line number="436" hits="0" branch="False" />
            <line number="437" hits="0" branch="False" />
            <line number="438" hits="0" branch="False" />
            <line number="439" hits="0" branch="False" />
            <line number="440" hits="0" branch="False" />
            <line number="441" hits="0" branch="False" />
            <line number="447" hits="0" branch="False" />
            <line number="448" hits="0" branch="True" condition-coverage="0% (0/6)">
              <conditions>
                <condition number="8" type="jump" coverage="0%" />
                <condition number="25" type="jump" coverage="0%" />
                <condition number="38" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="449" hits="0" branch="False" />
            <line number="451" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="49" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="452" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="81" type="jump" coverage="0%" />
                <condition number="103" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="453" hits="0" branch="False" />
            <line number="454" hits="0" branch="False" />
            <line number="455" hits="0" branch="False" />
            <line number="456" hits="0" branch="False" />
            <line number="457" hits="0" branch="False" />
            <line number="458" hits="0" branch="False" />
            <line number="459" hits="0" branch="False" />
            <line number="460" hits="0" branch="False" />
            <line number="461" hits="0" branch="False" />
            <line number="462" hits="0" branch="False" />
            <line number="463" hits="0" branch="False" />
            <line number="464" hits="0" branch="False" />
            <line number="465" hits="0" branch="False" />
            <line number="466" hits="0" branch="False" />
            <line number="467" hits="0" branch="False" />
            <line number="468" hits="0" branch="False" />
            <line number="469" hits="0" branch="False" />
            <line number="470" hits="0" branch="False" />
            <line number="471" hits="0" branch="False" />
            <line number="472" hits="0" branch="False" />
            <line number="473" hits="0" branch="False" />
            <line number="474" hits="0" branch="False" />
            <line number="475" hits="0" branch="False" />
            <line number="476" hits="0" branch="False" />
            <line number="477" hits="0" branch="False" />
            <line number="478" hits="0" branch="False" />
            <line number="479" hits="0" branch="False" />
            <line number="480" hits="0" branch="False" />
            <line number="481" hits="0" branch="False" />
            <line number="482" hits="0" branch="False" />
            <line number="483" hits="0" branch="False" />
            <line number="484" hits="0" branch="False" />
            <line number="485" hits="0" branch="False" />
            <line number="486" hits="0" branch="False" />
            <line number="492" hits="0" branch="False" />
            <line number="493" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="8" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="494" hits="0" branch="False" />
            <line number="496" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="33" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="497" hits="0" branch="False" />
            <line number="498" hits="0" branch="False" />
            <line number="504" hits="0" branch="False" />
            <line number="506" hits="0" branch="False" />
            <line number="507" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="13" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="508" hits="0" branch="False" />
            <line number="509" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="29" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="510" hits="0" branch="False" />
            <line number="511" hits="0" branch="False" />
            <line number="512" hits="0" branch="False" />
            <line number="513" hits="0" branch="False" />
            <line number="514" hits="0" branch="False" />
            <line number="515" hits="0" branch="False" />
            <line number="516" hits="0" branch="False" />
            <line number="517" hits="0" branch="False" />
            <line number="518" hits="0" branch="False" />
            <line number="519" hits="0" branch="False" />
            <line number="520" hits="0" branch="False" />
            <line number="521" hits="0" branch="False" />
            <line number="527" hits="0" branch="False" />
            <line number="528" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="529" hits="0" branch="False" />
            <line number="532" hits="0" branch="False" />
            <line number="533" hits="0" branch="False" />
            <line number="534" hits="0" branch="False" />
            <line number="535" hits="0" branch="False" />
            <line number="536" hits="0" branch="False" />
            <line number="537" hits="0" branch="False" />
            <line number="538" hits="0" branch="False" />
            <line number="539" hits="0" branch="False" />
            <line number="541" hits="0" branch="False" />
            <line number="542" hits="0" branch="False" />
            <line number="543" hits="0" branch="False" />
            <line number="544" hits="0" branch="False" />
            <line number="157" hits="0" branch="False" />
            <line number="158" hits="0" branch="False" />
            <line number="159" hits="0" branch="False" />
            <line number="160" hits="0" branch="False" />
            <line number="249" hits="0" branch="False" />
            <line number="250" hits="0" branch="False" />
            <line number="251" hits="0" branch="False" />
            <line number="307" hits="0" branch="False" />
            <line number="308" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="8" type="jump" coverage="0%" />
                <condition number="24" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="309" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="33" type="jump" coverage="0%" />
                <condition number="45" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="312" hits="0" branch="False" />
            <line number="313" hits="0" branch="False" />
            <line number="17" hits="0" branch="False" />
            <line number="18" hits="0" branch="False" />
            <line number="21" hits="0" branch="False" />
            <line number="73" hits="0" branch="False" />
            <line number="74" hits="0" branch="False" />
            <line number="75" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="40" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="76" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortConnection/&lt;ConnectAsync&gt;d__30" filename="Services\SerialPortConnection.cs" line-rate="0" branch-rate="0" complexity="4">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="86" hits="0" branch="False" />
                <line number="87" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="31" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="88" hits="0" branch="False" />
                <line number="90" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="60" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="91" hits="0" branch="False" />
                <line number="93" hits="0" branch="False" />
                <line number="94" hits="0" branch="False" />
                <line number="95" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="86" hits="0" branch="False" />
            <line number="87" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="31" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="88" hits="0" branch="False" />
            <line number="90" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="60" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="91" hits="0" branch="False" />
            <line number="93" hits="0" branch="False" />
            <line number="94" hits="0" branch="False" />
            <line number="95" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortConnection/&lt;ConnectAsync&gt;d__31" filename="Services\SerialPortConnection.cs" line-rate="0" branch-rate="0" complexity="20">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="20">
              <lines>
                <line number="105" hits="0" branch="False" />
                <line number="106" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="30" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="107" hits="0" branch="False" />
                <line number="109" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="54" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="110" hits="0" branch="False" />
                <line number="113" hits="0" branch="False" />
                <line number="114" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="102" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="115" hits="0" branch="False" />
                <line number="116" hits="0" branch="False" />
                <line number="117" hits="0" branch="False" />
                <line number="120" hits="0" branch="False" />
                <line number="121" hits="0" branch="False" />
                <line number="122" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="231" type="jump" coverage="0%" />
                    <condition number="254" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="123" hits="0" branch="False" />
                <line number="124" hits="0" branch="False" />
                <line number="125" hits="0" branch="False" />
                <line number="128" hits="0" branch="False" />
                <line number="129" hits="0" branch="False" />
                <line number="132" hits="0" branch="False" />
                <line number="133" hits="0" branch="False" />
                <line number="136" hits="0" branch="False" />
                <line number="137" hits="0" branch="False" />
                <line number="138" hits="0" branch="False" />
                <line number="139" hits="0" branch="False" />
                <line number="140" hits="0" branch="False" />
                <line number="141" hits="0" branch="False" />
                <line number="142" hits="0" branch="False" />
                <line number="143" hits="0" branch="False" />
                <line number="144" hits="0" branch="False" />
                <line number="145" hits="0" branch="False" />
                <line number="146" hits="0" branch="False" />
                <line number="147" hits="0" branch="False" />
                <line number="148" hits="0" branch="False" />
                <line number="149" hits="0" branch="False" />
                <line number="150" hits="0" branch="False" />
                <line number="151" hits="0" branch="False" />
                <line number="152" hits="0" branch="False" />
                <line number="153" hits="0" branch="False" />
                <line number="156" hits="0" branch="False" />
                <line number="162" hits="0" branch="False" />
                <line number="163" hits="0" branch="False" />
                <line number="165" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="948" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="166" hits="0" branch="False" />
                <line number="167" hits="0" branch="False" />
                <line number="170" hits="0" branch="False" />
                <line number="171" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="1113" type="jump" coverage="0%" />
                    <condition number="1138" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="172" hits="0" branch="False" />
                <line number="173" hits="0" branch="False" />
                <line number="174" hits="0" branch="False" />
                <line number="175" hits="0" branch="False" />
                <line number="178" hits="0" branch="False" />
                <line number="181" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="1225" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="182" hits="0" branch="False" />
                <line number="183" hits="0" branch="False" />
                <line number="184" hits="0" branch="False" />
                <line number="186" hits="0" branch="False" />
                <line number="187" hits="0" branch="False" />
                <line number="188" hits="0" branch="False" />
                <line number="191" hits="0" branch="False" />
                <line number="192" hits="0" branch="False" />
                <line number="195" hits="0" branch="False" />
                <line number="196" hits="0" branch="False" />
                <line number="197" hits="0" branch="False" />
                <line number="198" hits="0" branch="False" />
                <line number="199" hits="0" branch="False" />
                <line number="201" hits="0" branch="False" />
                <line number="202" hits="0" branch="False" />
                <line number="203" hits="0" branch="False" />
                <line number="204" hits="0" branch="False" />
                <line number="206" hits="0" branch="False" />
                <line number="207" hits="0" branch="False" />
                <line number="209" hits="0" branch="False" />
                <line number="212" hits="0" branch="False" />
                <line number="215" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="1613" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="216" hits="0" branch="False" />
                <line number="217" hits="0" branch="False" />
                <line number="218" hits="0" branch="False" />
                <line number="220" hits="0" branch="False" />
                <line number="222" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="105" hits="0" branch="False" />
            <line number="106" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="30" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="107" hits="0" branch="False" />
            <line number="109" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="54" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="110" hits="0" branch="False" />
            <line number="113" hits="0" branch="False" />
            <line number="114" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="102" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="115" hits="0" branch="False" />
            <line number="116" hits="0" branch="False" />
            <line number="117" hits="0" branch="False" />
            <line number="120" hits="0" branch="False" />
            <line number="121" hits="0" branch="False" />
            <line number="122" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="231" type="jump" coverage="0%" />
                <condition number="254" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="123" hits="0" branch="False" />
            <line number="124" hits="0" branch="False" />
            <line number="125" hits="0" branch="False" />
            <line number="128" hits="0" branch="False" />
            <line number="129" hits="0" branch="False" />
            <line number="132" hits="0" branch="False" />
            <line number="133" hits="0" branch="False" />
            <line number="136" hits="0" branch="False" />
            <line number="137" hits="0" branch="False" />
            <line number="138" hits="0" branch="False" />
            <line number="139" hits="0" branch="False" />
            <line number="140" hits="0" branch="False" />
            <line number="141" hits="0" branch="False" />
            <line number="142" hits="0" branch="False" />
            <line number="143" hits="0" branch="False" />
            <line number="144" hits="0" branch="False" />
            <line number="145" hits="0" branch="False" />
            <line number="146" hits="0" branch="False" />
            <line number="147" hits="0" branch="False" />
            <line number="148" hits="0" branch="False" />
            <line number="149" hits="0" branch="False" />
            <line number="150" hits="0" branch="False" />
            <line number="151" hits="0" branch="False" />
            <line number="152" hits="0" branch="False" />
            <line number="153" hits="0" branch="False" />
            <line number="156" hits="0" branch="False" />
            <line number="162" hits="0" branch="False" />
            <line number="163" hits="0" branch="False" />
            <line number="165" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="948" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="166" hits="0" branch="False" />
            <line number="167" hits="0" branch="False" />
            <line number="170" hits="0" branch="False" />
            <line number="171" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="1113" type="jump" coverage="0%" />
                <condition number="1138" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="172" hits="0" branch="False" />
            <line number="173" hits="0" branch="False" />
            <line number="174" hits="0" branch="False" />
            <line number="175" hits="0" branch="False" />
            <line number="178" hits="0" branch="False" />
            <line number="181" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="1225" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="182" hits="0" branch="False" />
            <line number="183" hits="0" branch="False" />
            <line number="184" hits="0" branch="False" />
            <line number="186" hits="0" branch="False" />
            <line number="187" hits="0" branch="False" />
            <line number="188" hits="0" branch="False" />
            <line number="191" hits="0" branch="False" />
            <line number="192" hits="0" branch="False" />
            <line number="195" hits="0" branch="False" />
            <line number="196" hits="0" branch="False" />
            <line number="197" hits="0" branch="False" />
            <line number="198" hits="0" branch="False" />
            <line number="199" hits="0" branch="False" />
            <line number="201" hits="0" branch="False" />
            <line number="202" hits="0" branch="False" />
            <line number="203" hits="0" branch="False" />
            <line number="204" hits="0" branch="False" />
            <line number="206" hits="0" branch="False" />
            <line number="207" hits="0" branch="False" />
            <line number="209" hits="0" branch="False" />
            <line number="212" hits="0" branch="False" />
            <line number="215" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="1613" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="216" hits="0" branch="False" />
            <line number="217" hits="0" branch="False" />
            <line number="218" hits="0" branch="False" />
            <line number="220" hits="0" branch="False" />
            <line number="222" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortConnection/&lt;DisconnectAsync&gt;d__32" filename="Services\SerialPortConnection.cs" line-rate="0" branch-rate="0" complexity="20">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="20">
              <lines>
                <line number="229" hits="0" branch="False" />
                <line number="230" hits="0" branch="False" />
                <line number="231" hits="0" branch="False" />
                <line number="232" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="72" type="jump" coverage="0%" />
                    <condition number="93" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="233" hits="0" branch="False" />
                <line number="234" hits="0" branch="False" />
                <line number="237" hits="0" branch="False" />
                <line number="238" hits="0" branch="False" />
                <line number="241" hits="0" branch="False" />
                <line number="242" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="202" type="jump" coverage="0%" />
                    <condition number="214" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="245" hits="0" branch="False" />
                <line number="248" hits="0" branch="False" />
                <line number="253" hits="0" branch="False" />
                <line number="254" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="413" type="jump" coverage="0%" />
                    <condition number="425" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="255" hits="0" branch="False" />
                <line number="256" hits="0" branch="False" />
                <line number="257" hits="0" branch="False" />
                <line number="258" hits="0" branch="False" />
                <line number="259" hits="0" branch="False" />
                <line number="261" hits="0" branch="False" />
                <line number="262" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="544" type="jump" coverage="0%" />
                    <condition number="556" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="264" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="631" type="jump" coverage="0%" />
                    <condition number="643" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="265" hits="0" branch="False" />
                <line number="266" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="229" hits="0" branch="False" />
            <line number="230" hits="0" branch="False" />
            <line number="231" hits="0" branch="False" />
            <line number="232" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="72" type="jump" coverage="0%" />
                <condition number="93" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="233" hits="0" branch="False" />
            <line number="234" hits="0" branch="False" />
            <line number="237" hits="0" branch="False" />
            <line number="238" hits="0" branch="False" />
            <line number="241" hits="0" branch="False" />
            <line number="242" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="202" type="jump" coverage="0%" />
                <condition number="214" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="245" hits="0" branch="False" />
            <line number="248" hits="0" branch="False" />
            <line number="253" hits="0" branch="False" />
            <line number="254" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="413" type="jump" coverage="0%" />
                <condition number="425" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="255" hits="0" branch="False" />
            <line number="256" hits="0" branch="False" />
            <line number="257" hits="0" branch="False" />
            <line number="258" hits="0" branch="False" />
            <line number="259" hits="0" branch="False" />
            <line number="261" hits="0" branch="False" />
            <line number="262" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="544" type="jump" coverage="0%" />
                <condition number="556" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="264" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="631" type="jump" coverage="0%" />
                <condition number="643" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="265" hits="0" branch="False" />
            <line number="266" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortConnection/&lt;DisposeAsync&gt;d__45" filename="Services\SerialPortConnection.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="550" hits="0" branch="False" />
                <line number="551" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="28" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="552" hits="0" branch="False" />
                <line number="555" hits="0" branch="False" />
                <line number="556" hits="0" branch="False" />
                <line number="557" hits="0" branch="False" />
                <line number="558" hits="0" branch="False" />
                <line number="559" hits="0" branch="False" />
                <line number="560" hits="0" branch="False" />
                <line number="561" hits="0" branch="False" />
                <line number="562" hits="0" branch="False" />
                <line number="564" hits="0" branch="False" />
                <line number="565" hits="0" branch="False" />
                <line number="566" hits="0" branch="False" />
                <line number="567" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="550" hits="0" branch="False" />
            <line number="551" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="28" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="552" hits="0" branch="False" />
            <line number="555" hits="0" branch="False" />
            <line number="556" hits="0" branch="False" />
            <line number="557" hits="0" branch="False" />
            <line number="558" hits="0" branch="False" />
            <line number="559" hits="0" branch="False" />
            <line number="560" hits="0" branch="False" />
            <line number="561" hits="0" branch="False" />
            <line number="562" hits="0" branch="False" />
            <line number="564" hits="0" branch="False" />
            <line number="565" hits="0" branch="False" />
            <line number="566" hits="0" branch="False" />
            <line number="567" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortConnection/&lt;ReconnectAsync&gt;d__33" filename="Services\SerialPortConnection.cs" line-rate="0" branch-rate="0" complexity="4">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="274" hits="0" branch="False" />
                <line number="275" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="54" type="jump" coverage="0%" />
                    <condition number="75" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="276" hits="0" branch="False" />
                <line number="277" hits="0" branch="False" />
                <line number="278" hits="0" branch="False" />
                <line number="281" hits="0" branch="False" />
                <line number="284" hits="0" branch="False" />
                <line number="287" hits="0" branch="False" />
                <line number="290" hits="0" branch="False" />
                <line number="291" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="274" hits="0" branch="False" />
            <line number="275" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="54" type="jump" coverage="0%" />
                <condition number="75" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="276" hits="0" branch="False" />
            <line number="277" hits="0" branch="False" />
            <line number="278" hits="0" branch="False" />
            <line number="281" hits="0" branch="False" />
            <line number="284" hits="0" branch="False" />
            <line number="287" hits="0" branch="False" />
            <line number="290" hits="0" branch="False" />
            <line number="291" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortConnection/&lt;TestConnectionAsync&gt;d__34" filename="Services\SerialPortConnection.cs" line-rate="0" branch-rate="0" complexity="6">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="6">
              <lines>
                <line number="299" hits="0" branch="False" />
                <line number="300" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="31" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="301" hits="0" branch="False" />
                <line number="304" hits="0" branch="False" />
                <line number="306" hits="0" branch="False" />
                <line number="310" hits="0" branch="False" />
                <line number="311" hits="0" branch="False" />
                <line number="315" hits="0" branch="False" />
                <line number="317" hits="0" branch="False" />
                <line number="318" hits="0" branch="False" />
                <line number="319" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="225" type="jump" coverage="0%" />
                    <condition number="237" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="320" hits="0" branch="False" />
                <line number="322" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="299" hits="0" branch="False" />
            <line number="300" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="31" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="301" hits="0" branch="False" />
            <line number="304" hits="0" branch="False" />
            <line number="306" hits="0" branch="False" />
            <line number="310" hits="0" branch="False" />
            <line number="311" hits="0" branch="False" />
            <line number="315" hits="0" branch="False" />
            <line number="317" hits="0" branch="False" />
            <line number="318" hits="0" branch="False" />
            <line number="319" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="225" type="jump" coverage="0%" />
                <condition number="237" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="320" hits="0" branch="False" />
            <line number="322" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortConnection/&lt;&lt;StartAutoReconnect&gt;b__40_0&gt;d" filename="Services\SerialPortConnection.cs" line-rate="1" branch-rate="0" complexity="2">
          <methods />
          <lines />
        </class>
        <class name="Liam.SerialPort.Services.SerialPortConnection/&lt;&lt;StartHeartbeat&gt;b__41_0&gt;d" filename="Services\SerialPortConnection.cs" line-rate="1" branch-rate="0" complexity="14">
          <methods />
          <lines />
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="0" complexity="54">
          <methods>
            <method name="get_BytesToRead" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="45" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="7" type="jump" coverage="0%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name="get_BytesToWrite" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="50" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="7" type="jump" coverage="0%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name="get_ReceiveBufferSize" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="55" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="7" type="jump" coverage="0%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name="get_SendBufferSize" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="60" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="7" type="jump" coverage="0%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name="SetSerialPort" signature="(System.IO.Ports.SerialPort,System.String)" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="77" hits="0" branch="False" />
                <line number="78" hits="0" branch="False" />
                <line number="79" hits="0" branch="False" />
                <line number="80" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="23" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="81" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="45" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="84" hits="0" branch="False" />
                <line number="85" hits="0" branch="False" />
                <line number="86" hits="0" branch="False" />
                <line number="87" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ClearReceiveBuffer" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="380" hits="0" branch="False" />
                <line number="382" hits="0" branch="False" />
                <line number="383" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="386" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="39" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="388" hits="0" branch="False" />
                <line number="389" hits="0" branch="False" />
                <line number="390" hits="0" branch="False" />
                <line number="391" hits="0" branch="False" />
                <line number="392" hits="0" branch="False" />
                <line number="393" hits="0" branch="False" />
                <line number="394" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ClearSendBuffer" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="400" hits="0" branch="False" />
                <line number="402" hits="0" branch="False" />
                <line number="403" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="404" hits="0" branch="False" />
                <line number="405" hits="0" branch="False" />
                <line number="406" hits="0" branch="False" />
                <line number="407" hits="0" branch="False" />
                <line number="408" hits="0" branch="False" />
                <line number="409" hits="0" branch="False" />
                <line number="410" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ClearAllBuffers" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="416" hits="0" branch="False" />
                <line number="417" hits="0" branch="False" />
                <line number="418" hits="0" branch="False" />
                <line number="419" hits="0" branch="False" />
              </lines>
            </method>
            <method name="StartListening" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="425" hits="0" branch="False" />
                <line number="426" hits="0" branch="False" />
                <line number="427" hits="0" branch="False" />
                <line number="428" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="28" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="429" hits="0" branch="False" />
                <line number="431" hits="0" branch="False" />
                <line number="432" hits="0" branch="False" />
                <line number="433" hits="0" branch="False" />
                <line number="434" hits="0" branch="False" />
              </lines>
            </method>
            <method name="StopListening" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="440" hits="0" branch="False" />
                <line number="441" hits="0" branch="False" />
                <line number="442" hits="0" branch="False" />
                <line number="443" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="31" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="444" hits="0" branch="False" />
                <line number="446" hits="0" branch="False" />
                <line number="447" hits="0" branch="False" />
                <line number="448" hits="0" branch="False" />
                <line number="449" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnSerialPortDataReceived" signature="(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)" line-rate="0" branch-rate="0" complexity="10">
              <lines>
                <line number="457" hits="0" branch="False" />
                <line number="458" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="7" type="jump" coverage="0%" />
                    <condition number="20" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="459" hits="0" branch="False" />
                <line number="462" hits="0" branch="False" />
                <line number="463" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="36" type="jump" coverage="0%" />
                    <condition number="52" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="464" hits="0" branch="False" />
                <line number="465" hits="0" branch="False" />
                <line number="466" hits="0" branch="False" />
                <line number="468" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="100" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="469" hits="0" branch="False" />
                <line number="470" hits="0" branch="False" />
                <line number="471" hits="0" branch="False" />
                <line number="473" hits="0" branch="False" />
                <line number="474" hits="0" branch="False" />
                <line number="476" hits="0" branch="False" />
                <line number="478" hits="0" branch="False" />
                <line number="479" hits="0" branch="False" />
                <line number="480" hits="0" branch="False" />
                <line number="481" hits="0" branch="False" />
                <line number="482" hits="0" branch="False" />
                <line number="483" hits="0" branch="False" />
                <line number="484" hits="0" branch="False" />
                <line number="485" hits="0" branch="False" />
                <line number="486" hits="0" branch="False" />
                <line number="487" hits="0" branch="False" />
                <line number="488" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnSerialPortErrorReceived" signature="(System.Object,System.IO.Ports.SerialErrorReceivedEventArgs)" line-rate="0" branch-rate="0" complexity="9">
              <lines>
                <line number="496" hits="0" branch="False" />
                <line number="497" hits="0" branch="True" condition-coverage="0% (0/9)">
                  <conditions>
                    <condition number="15" type="switch" coverage="0%" />
                    <condition number="40" type="jump" coverage="0%" />
                    <condition number="50" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="498" hits="0" branch="False" />
                <line number="499" hits="0" branch="False" />
                <line number="500" hits="0" branch="False" />
                <line number="501" hits="0" branch="False" />
                <line number="502" hits="0" branch="False" />
                <line number="503" hits="0" branch="False" />
                <line number="504" hits="0" branch="False" />
                <line number="505" hits="0" branch="False" />
                <line number="507" hits="0" branch="False" />
                <line number="508" hits="0" branch="False" />
                <line number="510" hits="0" branch="False" />
                <line number="511" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnDataReceived" signature="(Liam.SerialPort.Events.DataReceivedEventArgs)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="518" hits="0" branch="False" />
                <line number="520" hits="0" branch="False" />
                <line number="521" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="522" hits="0" branch="False" />
                <line number="523" hits="0" branch="False" />
                <line number="524" hits="0" branch="False" />
                <line number="525" hits="0" branch="False" />
                <line number="526" hits="0" branch="False" />
                <line number="527" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnDataSent" signature="(Liam.SerialPort.Events.DataSentEventArgs)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="534" hits="0" branch="False" />
                <line number="536" hits="0" branch="False" />
                <line number="537" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="538" hits="0" branch="False" />
                <line number="539" hits="0" branch="False" />
                <line number="540" hits="0" branch="False" />
                <line number="541" hits="0" branch="False" />
                <line number="542" hits="0" branch="False" />
                <line number="543" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnErrorOccurred" signature="(Liam.SerialPort.Events.SerialPortErrorEventArgs)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="550" hits="0" branch="False" />
                <line number="552" hits="0" branch="False" />
                <line number="553" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="554" hits="0" branch="False" />
                <line number="555" hits="0" branch="False" />
                <line number="556" hits="0" branch="False" />
                <line number="557" hits="0" branch="False" />
                <line number="558" hits="0" branch="False" />
                <line number="559" hits="0" branch="False" />
              </lines>
            </method>
            <method name="Dispose" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="565" hits="0" branch="False" />
                <line number="566" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="567" hits="0" branch="False" />
                <line number="570" hits="0" branch="False" />
                <line number="571" hits="0" branch="False" />
                <line number="573" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="33" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="574" hits="0" branch="False" />
                <line number="575" hits="0" branch="False" />
                <line number="576" hits="0" branch="False" />
                <line number="577" hits="0" branch="False" />
                <line number="579" hits="0" branch="False" />
                <line number="580" hits="0" branch="False" />
                <line number="581" hits="0" branch="False" />
                <line number="582" hits="0" branch="False" />
                <line number="583" hits="0" branch="False" />
                <line number="584" hits="0" branch="False" />
                <line number="586" hits="0" branch="False" />
                <line number="587" hits="0" branch="False" />
                <line number="588" hits="0" branch="False" />
                <line number="589" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(Microsoft.Extensions.Logging.ILogger`1&lt;Liam.SerialPort.Services.SerialPortDataHandler&gt;)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="18" hits="0" branch="False" />
                <line number="19" hits="0" branch="False" />
                <line number="20" hits="0" branch="False" />
                <line number="23" hits="0" branch="False" />
                <line number="66" hits="0" branch="False" />
                <line number="67" hits="0" branch="False" />
                <line number="68" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="56" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="69" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="45" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="7" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="50" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="7" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="55" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="7" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="60" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="7" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="77" hits="0" branch="False" />
            <line number="78" hits="0" branch="False" />
            <line number="79" hits="0" branch="False" />
            <line number="80" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="23" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="81" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="45" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="84" hits="0" branch="False" />
            <line number="85" hits="0" branch="False" />
            <line number="86" hits="0" branch="False" />
            <line number="87" hits="0" branch="False" />
            <line number="380" hits="0" branch="False" />
            <line number="382" hits="0" branch="False" />
            <line number="383" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="386" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="39" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="388" hits="0" branch="False" />
            <line number="389" hits="0" branch="False" />
            <line number="390" hits="0" branch="False" />
            <line number="391" hits="0" branch="False" />
            <line number="392" hits="0" branch="False" />
            <line number="393" hits="0" branch="False" />
            <line number="394" hits="0" branch="False" />
            <line number="400" hits="0" branch="False" />
            <line number="402" hits="0" branch="False" />
            <line number="403" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="404" hits="0" branch="False" />
            <line number="405" hits="0" branch="False" />
            <line number="406" hits="0" branch="False" />
            <line number="407" hits="0" branch="False" />
            <line number="408" hits="0" branch="False" />
            <line number="409" hits="0" branch="False" />
            <line number="410" hits="0" branch="False" />
            <line number="416" hits="0" branch="False" />
            <line number="417" hits="0" branch="False" />
            <line number="418" hits="0" branch="False" />
            <line number="419" hits="0" branch="False" />
            <line number="425" hits="0" branch="False" />
            <line number="426" hits="0" branch="False" />
            <line number="427" hits="0" branch="False" />
            <line number="428" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="28" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="429" hits="0" branch="False" />
            <line number="431" hits="0" branch="False" />
            <line number="432" hits="0" branch="False" />
            <line number="433" hits="0" branch="False" />
            <line number="434" hits="0" branch="False" />
            <line number="440" hits="0" branch="False" />
            <line number="441" hits="0" branch="False" />
            <line number="442" hits="0" branch="False" />
            <line number="443" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="31" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="444" hits="0" branch="False" />
            <line number="446" hits="0" branch="False" />
            <line number="447" hits="0" branch="False" />
            <line number="448" hits="0" branch="False" />
            <line number="449" hits="0" branch="False" />
            <line number="457" hits="0" branch="False" />
            <line number="458" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="7" type="jump" coverage="0%" />
                <condition number="20" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="459" hits="0" branch="False" />
            <line number="462" hits="0" branch="False" />
            <line number="463" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="36" type="jump" coverage="0%" />
                <condition number="52" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="464" hits="0" branch="False" />
            <line number="465" hits="0" branch="False" />
            <line number="466" hits="0" branch="False" />
            <line number="468" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="100" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="469" hits="0" branch="False" />
            <line number="470" hits="0" branch="False" />
            <line number="471" hits="0" branch="False" />
            <line number="473" hits="0" branch="False" />
            <line number="474" hits="0" branch="False" />
            <line number="476" hits="0" branch="False" />
            <line number="478" hits="0" branch="False" />
            <line number="479" hits="0" branch="False" />
            <line number="480" hits="0" branch="False" />
            <line number="481" hits="0" branch="False" />
            <line number="482" hits="0" branch="False" />
            <line number="483" hits="0" branch="False" />
            <line number="484" hits="0" branch="False" />
            <line number="485" hits="0" branch="False" />
            <line number="486" hits="0" branch="False" />
            <line number="487" hits="0" branch="False" />
            <line number="488" hits="0" branch="False" />
            <line number="496" hits="0" branch="False" />
            <line number="497" hits="0" branch="True" condition-coverage="0% (0/9)">
              <conditions>
                <condition number="15" type="switch" coverage="0%" />
                <condition number="40" type="jump" coverage="0%" />
                <condition number="50" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="498" hits="0" branch="False" />
            <line number="499" hits="0" branch="False" />
            <line number="500" hits="0" branch="False" />
            <line number="501" hits="0" branch="False" />
            <line number="502" hits="0" branch="False" />
            <line number="503" hits="0" branch="False" />
            <line number="504" hits="0" branch="False" />
            <line number="505" hits="0" branch="False" />
            <line number="507" hits="0" branch="False" />
            <line number="508" hits="0" branch="False" />
            <line number="510" hits="0" branch="False" />
            <line number="511" hits="0" branch="False" />
            <line number="518" hits="0" branch="False" />
            <line number="520" hits="0" branch="False" />
            <line number="521" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="522" hits="0" branch="False" />
            <line number="523" hits="0" branch="False" />
            <line number="524" hits="0" branch="False" />
            <line number="525" hits="0" branch="False" />
            <line number="526" hits="0" branch="False" />
            <line number="527" hits="0" branch="False" />
            <line number="534" hits="0" branch="False" />
            <line number="536" hits="0" branch="False" />
            <line number="537" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="538" hits="0" branch="False" />
            <line number="539" hits="0" branch="False" />
            <line number="540" hits="0" branch="False" />
            <line number="541" hits="0" branch="False" />
            <line number="542" hits="0" branch="False" />
            <line number="543" hits="0" branch="False" />
            <line number="550" hits="0" branch="False" />
            <line number="552" hits="0" branch="False" />
            <line number="553" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="554" hits="0" branch="False" />
            <line number="555" hits="0" branch="False" />
            <line number="556" hits="0" branch="False" />
            <line number="557" hits="0" branch="False" />
            <line number="558" hits="0" branch="False" />
            <line number="559" hits="0" branch="False" />
            <line number="565" hits="0" branch="False" />
            <line number="566" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="567" hits="0" branch="False" />
            <line number="570" hits="0" branch="False" />
            <line number="571" hits="0" branch="False" />
            <line number="573" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="33" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="574" hits="0" branch="False" />
            <line number="575" hits="0" branch="False" />
            <line number="576" hits="0" branch="False" />
            <line number="577" hits="0" branch="False" />
            <line number="579" hits="0" branch="False" />
            <line number="580" hits="0" branch="False" />
            <line number="581" hits="0" branch="False" />
            <line number="582" hits="0" branch="False" />
            <line number="583" hits="0" branch="False" />
            <line number="584" hits="0" branch="False" />
            <line number="586" hits="0" branch="False" />
            <line number="587" hits="0" branch="False" />
            <line number="588" hits="0" branch="False" />
            <line number="589" hits="0" branch="False" />
            <line number="18" hits="0" branch="False" />
            <line number="19" hits="0" branch="False" />
            <line number="20" hits="0" branch="False" />
            <line number="23" hits="0" branch="False" />
            <line number="66" hits="0" branch="False" />
            <line number="67" hits="0" branch="False" />
            <line number="68" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="56" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="69" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler/&lt;&gt;c__DisplayClass27_0" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;SendAsync&gt;b__0" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="114" hits="0" branch="False" />
                <line number="115" hits="0" branch="False" />
                <line number="116" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="114" hits="0" branch="False" />
            <line number="115" hits="0" branch="False" />
            <line number="116" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler/&lt;&gt;c__DisplayClass30_0/&lt;&lt;ReadAsync&gt;b__0&gt;d" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="0" complexity="20">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="20">
              <lines>
                <line number="209" hits="0" branch="False" />
                <line number="210" hits="0" branch="False" />
                <line number="211" hits="0" branch="False" />
                <line number="213" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="579" type="jump" coverage="0%" />
                    <condition number="551" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="214" hits="0" branch="False" />
                <line number="215" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="96" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="216" hits="0" branch="False" />
                <line number="217" hits="0" branch="False" />
                <line number="218" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="142" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="220" hits="0" branch="False" />
                <line number="221" hits="0" branch="False" />
                <line number="223" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="261" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="224" hits="0" branch="False" />
                <line number="225" hits="0" branch="False" />
                <line number="227" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="305" type="jump" coverage="0%" />
                    <condition number="341" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="228" hits="0" branch="False" />
                <line number="229" hits="0" branch="False" />
                <line number="230" hits="0" branch="False" />
                <line number="232" hits="0" branch="True" condition-coverage="0% (0/6)">
                  <conditions>
                    <condition number="368" type="jump" coverage="0%" />
                    <condition number="382" type="jump" coverage="0%" />
                    <condition number="418" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="233" hits="0" branch="False" />
                <line number="234" hits="0" branch="False" />
                <line number="235" hits="0" branch="False" />
                <line number="236" hits="0" branch="False" />
                <line number="238" hits="0" branch="False" />
                <line number="239" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="209" hits="0" branch="False" />
            <line number="210" hits="0" branch="False" />
            <line number="211" hits="0" branch="False" />
            <line number="213" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="579" type="jump" coverage="0%" />
                <condition number="551" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="214" hits="0" branch="False" />
            <line number="215" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="96" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="216" hits="0" branch="False" />
            <line number="217" hits="0" branch="False" />
            <line number="218" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="142" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="220" hits="0" branch="False" />
            <line number="221" hits="0" branch="False" />
            <line number="223" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="261" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="224" hits="0" branch="False" />
            <line number="225" hits="0" branch="False" />
            <line number="227" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="305" type="jump" coverage="0%" />
                <condition number="341" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="228" hits="0" branch="False" />
            <line number="229" hits="0" branch="False" />
            <line number="230" hits="0" branch="False" />
            <line number="232" hits="0" branch="True" condition-coverage="0% (0/6)">
              <conditions>
                <condition number="368" type="jump" coverage="0%" />
                <condition number="382" type="jump" coverage="0%" />
                <condition number="418" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="233" hits="0" branch="False" />
            <line number="234" hits="0" branch="False" />
            <line number="235" hits="0" branch="False" />
            <line number="236" hits="0" branch="False" />
            <line number="238" hits="0" branch="False" />
            <line number="239" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler/&lt;&gt;c__DisplayClass32_0/&lt;&lt;ReadLineAsync&gt;b__0&gt;d" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="0" complexity="12">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="12">
              <lines>
                <line number="289" hits="0" branch="False" />
                <line number="290" hits="0" branch="False" />
                <line number="291" hits="0" branch="False" />
                <line number="292" hits="0" branch="False" />
                <line number="294" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="526" type="jump" coverage="0%" />
                    <condition number="498" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="295" hits="0" branch="False" />
                <line number="296" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="139" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="297" hits="0" branch="False" />
                <line number="298" hits="0" branch="False" />
                <line number="299" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="188" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="300" hits="0" branch="False" />
                <line number="301" hits="0" branch="False" />
                <line number="304" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="241" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="305" hits="0" branch="False" />
                <line number="306" hits="0" branch="False" />
                <line number="307" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="307" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="308" hits="0" branch="False" />
                <line number="310" hits="0" branch="False" />
                <line number="311" hits="0" branch="False" />
                <line number="313" hits="0" branch="False" />
                <line number="314" hits="0" branch="False" />
                <line number="315" hits="0" branch="False" />
                <line number="317" hits="0" branch="False" />
                <line number="318" hits="0" branch="False" />
                <line number="319" hits="0" branch="False" />
                <line number="320" hits="0" branch="False" />
                <line number="322" hits="0" branch="False" />
                <line number="323" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="289" hits="0" branch="False" />
            <line number="290" hits="0" branch="False" />
            <line number="291" hits="0" branch="False" />
            <line number="292" hits="0" branch="False" />
            <line number="294" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="526" type="jump" coverage="0%" />
                <condition number="498" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="295" hits="0" branch="False" />
            <line number="296" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="139" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="297" hits="0" branch="False" />
            <line number="298" hits="0" branch="False" />
            <line number="299" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="188" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="300" hits="0" branch="False" />
            <line number="301" hits="0" branch="False" />
            <line number="304" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="241" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="305" hits="0" branch="False" />
            <line number="306" hits="0" branch="False" />
            <line number="307" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="307" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="308" hits="0" branch="False" />
            <line number="310" hits="0" branch="False" />
            <line number="311" hits="0" branch="False" />
            <line number="313" hits="0" branch="False" />
            <line number="314" hits="0" branch="False" />
            <line number="315" hits="0" branch="False" />
            <line number="317" hits="0" branch="False" />
            <line number="318" hits="0" branch="False" />
            <line number="319" hits="0" branch="False" />
            <line number="320" hits="0" branch="False" />
            <line number="322" hits="0" branch="False" />
            <line number="323" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler/&lt;ReadAsync&gt;d__30" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="0" complexity="6">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="6">
              <lines>
                <line number="200" hits="0" branch="False" />
                <line number="201" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="109" type="jump" coverage="0%" />
                    <condition number="125" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="202" hits="0" branch="False" />
                <line number="204" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="182" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="207" hits="0" branch="False" />
                <line number="208" hits="0" branch="False" />
                <line number="212" hits="0" branch="False" />
                <line number="219" hits="0" branch="False" />
                <line number="222" hits="0" branch="False" />
                <line number="226" hits="0" branch="False" />
                <line number="231" hits="0" branch="False" />
                <line number="237" hits="0" branch="False" />
                <line number="241" hits="0" branch="False" />
                <line number="242" hits="0" branch="False" />
                <line number="243" hits="0" branch="False" />
                <line number="245" hits="0" branch="False" />
                <line number="246" hits="0" branch="False" />
                <line number="247" hits="0" branch="False" />
                <line number="249" hits="0" branch="False" />
                <line number="250" hits="0" branch="False" />
                <line number="251" hits="0" branch="False" />
                <line number="252" hits="0" branch="False" />
                <line number="253" hits="0" branch="False" />
                <line number="255" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="200" hits="0" branch="False" />
            <line number="201" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="109" type="jump" coverage="0%" />
                <condition number="125" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="202" hits="0" branch="False" />
            <line number="204" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="182" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="207" hits="0" branch="False" />
            <line number="208" hits="0" branch="False" />
            <line number="212" hits="0" branch="False" />
            <line number="219" hits="0" branch="False" />
            <line number="222" hits="0" branch="False" />
            <line number="226" hits="0" branch="False" />
            <line number="231" hits="0" branch="False" />
            <line number="237" hits="0" branch="False" />
            <line number="241" hits="0" branch="False" />
            <line number="242" hits="0" branch="False" />
            <line number="243" hits="0" branch="False" />
            <line number="245" hits="0" branch="False" />
            <line number="246" hits="0" branch="False" />
            <line number="247" hits="0" branch="False" />
            <line number="249" hits="0" branch="False" />
            <line number="250" hits="0" branch="False" />
            <line number="251" hits="0" branch="False" />
            <line number="252" hits="0" branch="False" />
            <line number="253" hits="0" branch="False" />
            <line number="255" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler/&lt;ReadLineAsync&gt;d__32" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="0" complexity="8">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="8">
              <lines>
                <line number="279" hits="0" branch="False" />
                <line number="280" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="109" type="jump" coverage="0%" />
                    <condition number="125" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="281" hits="0" branch="False" />
                <line number="283" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="160" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="284" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="211" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="287" hits="0" branch="False" />
                <line number="288" hits="0" branch="False" />
                <line number="293" hits="0" branch="False" />
                <line number="302" hits="0" branch="False" />
                <line number="303" hits="0" branch="False" />
                <line number="309" hits="0" branch="False" />
                <line number="312" hits="0" branch="False" />
                <line number="316" hits="0" branch="False" />
                <line number="321" hits="0" branch="False" />
                <line number="325" hits="0" branch="False" />
                <line number="326" hits="0" branch="False" />
                <line number="327" hits="0" branch="False" />
                <line number="329" hits="0" branch="False" />
                <line number="330" hits="0" branch="False" />
                <line number="331" hits="0" branch="False" />
                <line number="333" hits="0" branch="False" />
                <line number="334" hits="0" branch="False" />
                <line number="335" hits="0" branch="False" />
                <line number="336" hits="0" branch="False" />
                <line number="337" hits="0" branch="False" />
                <line number="339" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="279" hits="0" branch="False" />
            <line number="280" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="109" type="jump" coverage="0%" />
                <condition number="125" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="281" hits="0" branch="False" />
            <line number="283" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="160" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="284" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="211" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="287" hits="0" branch="False" />
            <line number="288" hits="0" branch="False" />
            <line number="293" hits="0" branch="False" />
            <line number="302" hits="0" branch="False" />
            <line number="303" hits="0" branch="False" />
            <line number="309" hits="0" branch="False" />
            <line number="312" hits="0" branch="False" />
            <line number="316" hits="0" branch="False" />
            <line number="321" hits="0" branch="False" />
            <line number="325" hits="0" branch="False" />
            <line number="326" hits="0" branch="False" />
            <line number="327" hits="0" branch="False" />
            <line number="329" hits="0" branch="False" />
            <line number="330" hits="0" branch="False" />
            <line number="331" hits="0" branch="False" />
            <line number="333" hits="0" branch="False" />
            <line number="334" hits="0" branch="False" />
            <line number="335" hits="0" branch="False" />
            <line number="336" hits="0" branch="False" />
            <line number="337" hits="0" branch="False" />
            <line number="339" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler/&lt;ReadStringAsync&gt;d__31" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="265" hits="0" branch="False" />
                <line number="266" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="21" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="267" hits="0" branch="False" />
                <line number="268" hits="0" branch="False" />
                <line number="269" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="265" hits="0" branch="False" />
            <line number="266" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="21" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="267" hits="0" branch="False" />
            <line number="268" hits="0" branch="False" />
            <line number="269" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler/&lt;SendAndReceiveAsync&gt;d__33" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="349" hits="0" branch="False" />
                <line number="351" hits="0" branch="False" />
                <line number="354" hits="0" branch="False" />
                <line number="357" hits="0" branch="False" />
                <line number="358" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="349" hits="0" branch="False" />
            <line number="351" hits="0" branch="False" />
            <line number="354" hits="0" branch="False" />
            <line number="357" hits="0" branch="False" />
            <line number="358" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler/&lt;SendAndReceiveAsync&gt;d__34" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="369" hits="0" branch="False" />
                <line number="370" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="21" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="371" hits="0" branch="False" />
                <line number="372" hits="0" branch="False" />
                <line number="373" hits="0" branch="False" />
                <line number="374" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="369" hits="0" branch="False" />
            <line number="370" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="21" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="371" hits="0" branch="False" />
            <line number="372" hits="0" branch="False" />
            <line number="373" hits="0" branch="False" />
            <line number="374" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler/&lt;SendAsync&gt;d__27" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="0" complexity="10">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="10">
              <lines>
                <line number="95" hits="0" branch="False" />
                <line number="96" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="79" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="97" hits="0" branch="False" />
                <line number="99" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="109" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="100" hits="0" branch="False" />
                <line number="102" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="128" type="jump" coverage="0%" />
                    <condition number="144" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="103" hits="0" branch="False" />
                <line number="105" hits="0" branch="False" />
                <line number="106" hits="0" branch="False" />
                <line number="107" hits="0" branch="False" />
                <line number="110" hits="0" branch="False" />
                <line number="111" hits="0" branch="False" />
                <line number="113" hits="0" branch="False" />
                <line number="118" hits="0" branch="False" />
                <line number="119" hits="0" branch="False" />
                <line number="120" hits="0" branch="False" />
                <line number="121" hits="0" branch="False" />
                <line number="122" hits="0" branch="False" />
                <line number="123" hits="0" branch="False" />
                <line number="124" hits="0" branch="False" />
                <line number="126" hits="0" branch="False" />
                <line number="127" hits="0" branch="False" />
                <line number="128" hits="0" branch="False" />
                <line number="129" hits="0" branch="False" />
                <line number="131" hits="0" branch="False" />
                <line number="132" hits="0" branch="False" />
                <line number="133" hits="0" branch="False" />
                <line number="134" hits="0" branch="False" />
                <line number="135" hits="0" branch="False" />
                <line number="138" hits="0" branch="False" />
                <line number="139" hits="0" branch="False" />
                <line number="140" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="778" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="141" hits="0" branch="False" />
                <line number="142" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="95" hits="0" branch="False" />
            <line number="96" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="79" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="97" hits="0" branch="False" />
            <line number="99" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="109" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="100" hits="0" branch="False" />
            <line number="102" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="128" type="jump" coverage="0%" />
                <condition number="144" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="103" hits="0" branch="False" />
            <line number="105" hits="0" branch="False" />
            <line number="106" hits="0" branch="False" />
            <line number="107" hits="0" branch="False" />
            <line number="110" hits="0" branch="False" />
            <line number="111" hits="0" branch="False" />
            <line number="113" hits="0" branch="False" />
            <line number="118" hits="0" branch="False" />
            <line number="119" hits="0" branch="False" />
            <line number="120" hits="0" branch="False" />
            <line number="121" hits="0" branch="False" />
            <line number="122" hits="0" branch="False" />
            <line number="123" hits="0" branch="False" />
            <line number="124" hits="0" branch="False" />
            <line number="126" hits="0" branch="False" />
            <line number="127" hits="0" branch="False" />
            <line number="128" hits="0" branch="False" />
            <line number="129" hits="0" branch="False" />
            <line number="131" hits="0" branch="False" />
            <line number="132" hits="0" branch="False" />
            <line number="133" hits="0" branch="False" />
            <line number="134" hits="0" branch="False" />
            <line number="135" hits="0" branch="False" />
            <line number="138" hits="0" branch="False" />
            <line number="139" hits="0" branch="False" />
            <line number="140" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="778" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="141" hits="0" branch="False" />
            <line number="142" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler/&lt;SendAsync&gt;d__28" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="0" complexity="4">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="151" hits="0" branch="False" />
                <line number="152" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="31" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="153" hits="0" branch="False" />
                <line number="155" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="44" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="156" hits="0" branch="False" />
                <line number="157" hits="0" branch="False" />
                <line number="158" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="151" hits="0" branch="False" />
            <line number="152" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="31" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="153" hits="0" branch="False" />
            <line number="155" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="44" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="156" hits="0" branch="False" />
            <line number="157" hits="0" branch="False" />
            <line number="158" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDataHandler/&lt;SendHexAsync&gt;d__29" filename="Services\SerialPortDataHandler.cs" line-rate="0" branch-rate="0" complexity="6">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="6">
              <lines>
                <line number="166" hits="0" branch="False" />
                <line number="167" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="28" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="168" hits="0" branch="False" />
                <line number="171" hits="0" branch="False" />
                <line number="173" hits="0" branch="False" />
                <line number="175" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="137" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="176" hits="0" branch="False" />
                <line number="178" hits="0" branch="False" />
                <line number="179" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="266" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="180" hits="0" branch="False" />
                <line number="181" hits="0" branch="False" />
                <line number="182" hits="0" branch="False" />
                <line number="184" hits="0" branch="False" />
                <line number="185" hits="0" branch="False" />
                <line number="186" hits="0" branch="False" />
                <line number="187" hits="0" branch="False" />
                <line number="188" hits="0" branch="False" />
                <line number="190" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="166" hits="0" branch="False" />
            <line number="167" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="28" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="168" hits="0" branch="False" />
            <line number="171" hits="0" branch="False" />
            <line number="173" hits="0" branch="False" />
            <line number="175" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="137" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="176" hits="0" branch="False" />
            <line number="178" hits="0" branch="False" />
            <line number="179" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="266" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="180" hits="0" branch="False" />
            <line number="181" hits="0" branch="False" />
            <line number="182" hits="0" branch="False" />
            <line number="184" hits="0" branch="False" />
            <line number="185" hits="0" branch="False" />
            <line number="186" hits="0" branch="False" />
            <line number="187" hits="0" branch="False" />
            <line number="188" hits="0" branch="False" />
            <line number="190" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="0" complexity="9">
          <methods>
            <method name="get_IsMonitoring" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="32" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnDeviceChanged" signature="(Liam.SerialPort.Events.DeviceChangedEventArgs)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="332" hits="0" branch="False" />
                <line number="334" hits="0" branch="False" />
                <line number="335" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="337" hits="0" branch="False" />
                <line number="338" hits="0" branch="False" />
                <line number="339" hits="0" branch="False" />
                <line number="340" hits="0" branch="False" />
                <line number="341" hits="0" branch="False" />
                <line number="342" hits="0" branch="False" />
                <line number="343" hits="0" branch="False" />
                <line number="344" hits="0" branch="False" />
              </lines>
            </method>
            <method name="Dispose" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="398" hits="0" branch="False" />
                <line number="399" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="400" hits="0" branch="False" />
                <line number="403" hits="0" branch="False" />
                <line number="404" hits="0" branch="False" />
                <line number="405" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="39" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="406" hits="0" branch="False" />
                <line number="407" hits="0" branch="False" />
                <line number="408" hits="0" branch="False" />
                <line number="409" hits="0" branch="False" />
                <line number="410" hits="0" branch="False" />
                <line number="412" hits="0" branch="False" />
                <line number="413" hits="0" branch="False" />
                <line number="414" hits="0" branch="False" />
                <line number="415" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(Microsoft.Extensions.Logging.ILogger`1&lt;Liam.SerialPort.Services.SerialPortDiscovery&gt;)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="18" hits="0" branch="False" />
                <line number="19" hits="0" branch="False" />
                <line number="38" hits="0" branch="False" />
                <line number="39" hits="0" branch="False" />
                <line number="40" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="33" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="43" hits="0" branch="False" />
                <line number="44" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="32" hits="0" branch="False" />
            <line number="332" hits="0" branch="False" />
            <line number="334" hits="0" branch="False" />
            <line number="335" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="337" hits="0" branch="False" />
            <line number="338" hits="0" branch="False" />
            <line number="339" hits="0" branch="False" />
            <line number="340" hits="0" branch="False" />
            <line number="341" hits="0" branch="False" />
            <line number="342" hits="0" branch="False" />
            <line number="343" hits="0" branch="False" />
            <line number="344" hits="0" branch="False" />
            <line number="398" hits="0" branch="False" />
            <line number="399" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="400" hits="0" branch="False" />
            <line number="403" hits="0" branch="False" />
            <line number="404" hits="0" branch="False" />
            <line number="405" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="39" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="406" hits="0" branch="False" />
            <line number="407" hits="0" branch="False" />
            <line number="408" hits="0" branch="False" />
            <line number="409" hits="0" branch="False" />
            <line number="410" hits="0" branch="False" />
            <line number="412" hits="0" branch="False" />
            <line number="413" hits="0" branch="False" />
            <line number="414" hits="0" branch="False" />
            <line number="415" hits="0" branch="False" />
            <line number="18" hits="0" branch="False" />
            <line number="19" hits="0" branch="False" />
            <line number="38" hits="0" branch="False" />
            <line number="39" hits="0" branch="False" />
            <line number="40" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="33" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="43" hits="0" branch="False" />
            <line number="44" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;&gt;c" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="1" complexity="2">
          <methods>
            <method name="&lt;GetAvailablePortsAsync&gt;b__12_0" signature="(Liam.SerialPort.Models.SerialPortInfo)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="86" hits="0" branch="False" />
              </lines>
            </method>
            <method name="&lt;CheckForDeviceChanges&gt;b__19_0" signature="(Liam.SerialPort.Models.SerialPortInfo)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="287" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="86" hits="0" branch="False" />
            <line number="287" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;&gt;c__DisplayClass14_0" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="&lt;IsPortAvailableAsync&gt;b__0" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="145" hits="0" branch="False" />
                <line number="147" hits="0" branch="False" />
                <line number="148" hits="0" branch="False" />
                <line number="149" hits="0" branch="False" />
                <line number="151" hits="0" branch="False" />
                <line number="152" hits="0" branch="False" />
                <line number="153" hits="0" branch="False" />
                <line number="156" hits="0" branch="False" />
                <line number="157" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="38" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="158" hits="0" branch="False" />
                <line number="159" hits="0" branch="False" />
                <line number="160" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="145" hits="0" branch="False" />
            <line number="147" hits="0" branch="False" />
            <line number="148" hits="0" branch="False" />
            <line number="149" hits="0" branch="False" />
            <line number="151" hits="0" branch="False" />
            <line number="152" hits="0" branch="False" />
            <line number="153" hits="0" branch="False" />
            <line number="156" hits="0" branch="False" />
            <line number="157" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="38" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="158" hits="0" branch="False" />
            <line number="159" hits="0" branch="False" />
            <line number="160" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;CheckForDeviceChanges&gt;d__19" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="0" complexity="14">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="14">
              <lines>
                <line number="286" hits="0" branch="False" />
                <line number="289" hits="0" branch="False" />
                <line number="290" hits="0" branch="False" />
                <line number="292" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="265" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="293" hits="0" branch="False" />
                <line number="294" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="184" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="295" hits="0" branch="False" />
                <line number="296" hits="0" branch="False" />
                <line number="297" hits="0" branch="False" />
                <line number="298" hits="0" branch="False" />
                <line number="299" hits="0" branch="False" />
                <line number="302" hits="0" branch="False" />
                <line number="303" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="489" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="304" hits="0" branch="False" />
                <line number="305" hits="0" branch="False" />
                <line number="306" hits="0" branch="False" />
                <line number="307" hits="0" branch="False" />
                <line number="308" hits="0" branch="False" />
                <line number="311" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="757" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="312" hits="0" branch="False" />
                <line number="313" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="609" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="314" hits="0" branch="False" />
                <line number="315" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="634" type="jump" coverage="0%" />
                    <condition number="668" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="316" hits="0" branch="False" />
                <line number="317" hits="0" branch="False" />
                <line number="318" hits="0" branch="False" />
                <line number="319" hits="0" branch="False" />
                <line number="320" hits="0" branch="False" />
                <line number="321" hits="0" branch="False" />
                <line number="322" hits="0" branch="False" />
                <line number="324" hits="0" branch="False" />
                <line number="325" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="286" hits="0" branch="False" />
            <line number="289" hits="0" branch="False" />
            <line number="290" hits="0" branch="False" />
            <line number="292" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="265" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="293" hits="0" branch="False" />
            <line number="294" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="184" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="295" hits="0" branch="False" />
            <line number="296" hits="0" branch="False" />
            <line number="297" hits="0" branch="False" />
            <line number="298" hits="0" branch="False" />
            <line number="299" hits="0" branch="False" />
            <line number="302" hits="0" branch="False" />
            <line number="303" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="489" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="304" hits="0" branch="False" />
            <line number="305" hits="0" branch="False" />
            <line number="306" hits="0" branch="False" />
            <line number="307" hits="0" branch="False" />
            <line number="308" hits="0" branch="False" />
            <line number="311" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="757" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="312" hits="0" branch="False" />
            <line number="313" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="609" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="314" hits="0" branch="False" />
            <line number="315" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="634" type="jump" coverage="0%" />
                <condition number="668" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="316" hits="0" branch="False" />
            <line number="317" hits="0" branch="False" />
            <line number="318" hits="0" branch="False" />
            <line number="319" hits="0" branch="False" />
            <line number="320" hits="0" branch="False" />
            <line number="321" hits="0" branch="False" />
            <line number="322" hits="0" branch="False" />
            <line number="324" hits="0" branch="False" />
            <line number="325" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;EnhancePortInfo&gt;d__21" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="352" hits="0" branch="False" />
                <line number="354" hits="0" branch="False" />
                <line number="355" hits="0" branch="False" />
                <line number="357" hits="0" branch="False" />
                <line number="358" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="352" hits="0" branch="False" />
            <line number="354" hits="0" branch="False" />
            <line number="355" hits="0" branch="False" />
            <line number="357" hits="0" branch="False" />
            <line number="358" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;EnhancePortInfoForLinux&gt;d__23" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="378" hits="0" branch="False" />
                <line number="380" hits="0" branch="False" />
                <line number="381" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="378" hits="0" branch="False" />
            <line number="380" hits="0" branch="False" />
            <line number="381" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;EnhancePortInfoForMacOS&gt;d__24" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="389" hits="0" branch="False" />
                <line number="391" hits="0" branch="False" />
                <line number="392" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="389" hits="0" branch="False" />
            <line number="391" hits="0" branch="False" />
            <line number="392" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;EnhancePortInfoForWindows&gt;d__22" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="366" hits="0" branch="False" />
                <line number="369" hits="0" branch="False" />
                <line number="370" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="366" hits="0" branch="False" />
            <line number="369" hits="0" branch="False" />
            <line number="370" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;GetAvailablePortsAsync&gt;d__12" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="0" complexity="10">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="10">
              <lines>
                <line number="52" hits="0" branch="False" />
                <line number="54" hits="0" branch="False" />
                <line number="55" hits="0" branch="False" />
                <line number="57" hits="0" branch="False" />
                <line number="58" hits="0" branch="False" />
                <line number="60" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="383" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="61" hits="0" branch="False" />
                <line number="62" hits="0" branch="False" />
                <line number="64" hits="0" branch="False" />
                <line number="65" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="318" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="66" hits="0" branch="False" />
                <line number="67" hits="0" branch="False" />
                <line number="68" hits="0" branch="False" />
                <line number="69" hits="0" branch="False" />
                <line number="72" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="409" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="73" hits="0" branch="False" />
                <line number="74" hits="0" branch="False" />
                <line number="75" hits="0" branch="False" />
                <line number="76" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="548" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="77" hits="0" branch="False" />
                <line number="78" hits="0" branch="False" />
                <line number="79" hits="0" branch="False" />
                <line number="80" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="687" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="81" hits="0" branch="False" />
                <line number="82" hits="0" branch="False" />
                <line number="83" hits="0" branch="False" />
                <line number="85" hits="0" branch="False" />
                <line number="88" hits="0" branch="False" />
                <line number="89" hits="0" branch="False" />
                <line number="90" hits="0" branch="False" />
                <line number="91" hits="0" branch="False" />
                <line number="93" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="52" hits="0" branch="False" />
            <line number="54" hits="0" branch="False" />
            <line number="55" hits="0" branch="False" />
            <line number="57" hits="0" branch="False" />
            <line number="58" hits="0" branch="False" />
            <line number="60" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="383" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="61" hits="0" branch="False" />
            <line number="62" hits="0" branch="False" />
            <line number="64" hits="0" branch="False" />
            <line number="65" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="318" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="66" hits="0" branch="False" />
            <line number="67" hits="0" branch="False" />
            <line number="68" hits="0" branch="False" />
            <line number="69" hits="0" branch="False" />
            <line number="72" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="409" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="73" hits="0" branch="False" />
            <line number="74" hits="0" branch="False" />
            <line number="75" hits="0" branch="False" />
            <line number="76" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="548" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="77" hits="0" branch="False" />
            <line number="78" hits="0" branch="False" />
            <line number="79" hits="0" branch="False" />
            <line number="80" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="687" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="81" hits="0" branch="False" />
            <line number="82" hits="0" branch="False" />
            <line number="83" hits="0" branch="False" />
            <line number="85" hits="0" branch="False" />
            <line number="88" hits="0" branch="False" />
            <line number="89" hits="0" branch="False" />
            <line number="90" hits="0" branch="False" />
            <line number="91" hits="0" branch="False" />
            <line number="93" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;GetPortInfoAsync&gt;d__13" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="102" hits="0" branch="False" />
                <line number="103" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="29" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="104" hits="0" branch="False" />
                <line number="107" hits="0" branch="False" />
                <line number="108" hits="0" branch="False" />
                <line number="109" hits="0" branch="False" />
                <line number="110" hits="0" branch="False" />
                <line number="111" hits="0" branch="False" />
                <line number="112" hits="0" branch="False" />
                <line number="113" hits="0" branch="False" />
                <line number="114" hits="0" branch="False" />
                <line number="117" hits="0" branch="False" />
                <line number="119" hits="0" branch="False" />
                <line number="121" hits="0" branch="False" />
                <line number="122" hits="0" branch="False" />
                <line number="123" hits="0" branch="False" />
                <line number="124" hits="0" branch="False" />
                <line number="126" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="102" hits="0" branch="False" />
            <line number="103" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="29" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="104" hits="0" branch="False" />
            <line number="107" hits="0" branch="False" />
            <line number="108" hits="0" branch="False" />
            <line number="109" hits="0" branch="False" />
            <line number="110" hits="0" branch="False" />
            <line number="111" hits="0" branch="False" />
            <line number="112" hits="0" branch="False" />
            <line number="113" hits="0" branch="False" />
            <line number="114" hits="0" branch="False" />
            <line number="117" hits="0" branch="False" />
            <line number="119" hits="0" branch="False" />
            <line number="121" hits="0" branch="False" />
            <line number="122" hits="0" branch="False" />
            <line number="123" hits="0" branch="False" />
            <line number="124" hits="0" branch="False" />
            <line number="126" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;IsPortAvailableAsync&gt;d__14" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="135" hits="0" branch="False" />
                <line number="136" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="28" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="137" hits="0" branch="False" />
                <line number="140" hits="0" branch="False" />
                <line number="142" hits="0" branch="False" />
                <line number="144" hits="0" branch="False" />
                <line number="146" hits="0" branch="False" />
                <line number="150" hits="0" branch="False" />
                <line number="154" hits="0" branch="False" />
                <line number="155" hits="0" branch="False" />
                <line number="162" hits="0" branch="False" />
                <line number="164" hits="0" branch="False" />
                <line number="165" hits="0" branch="False" />
                <line number="166" hits="0" branch="False" />
                <line number="168" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="135" hits="0" branch="False" />
            <line number="136" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="28" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="137" hits="0" branch="False" />
            <line number="140" hits="0" branch="False" />
            <line number="142" hits="0" branch="False" />
            <line number="144" hits="0" branch="False" />
            <line number="146" hits="0" branch="False" />
            <line number="150" hits="0" branch="False" />
            <line number="154" hits="0" branch="False" />
            <line number="155" hits="0" branch="False" />
            <line number="162" hits="0" branch="False" />
            <line number="164" hits="0" branch="False" />
            <line number="165" hits="0" branch="False" />
            <line number="166" hits="0" branch="False" />
            <line number="168" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;MonitorDeviceChanges&gt;d__18" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="0" complexity="4">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="267" hits="0" branch="False" />
                <line number="268" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="26" type="jump" coverage="0%" />
                    <condition number="44" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="269" hits="0" branch="False" />
                <line number="272" hits="0" branch="False" />
                <line number="273" hits="0" branch="False" />
                <line number="274" hits="0" branch="False" />
                <line number="275" hits="0" branch="False" />
                <line number="276" hits="0" branch="False" />
                <line number="277" hits="0" branch="False" />
                <line number="278" hits="0" branch="False" />
                <line number="279" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="267" hits="0" branch="False" />
            <line number="268" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="26" type="jump" coverage="0%" />
                <condition number="44" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="269" hits="0" branch="False" />
            <line number="272" hits="0" branch="False" />
            <line number="273" hits="0" branch="False" />
            <line number="274" hits="0" branch="False" />
            <line number="275" hits="0" branch="False" />
            <line number="276" hits="0" branch="False" />
            <line number="277" hits="0" branch="False" />
            <line number="278" hits="0" branch="False" />
            <line number="279" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;RefreshAsync&gt;d__17" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="243" hits="0" branch="False" />
                <line number="245" hits="0" branch="False" />
                <line number="246" hits="0" branch="False" />
                <line number="248" hits="0" branch="False" />
                <line number="250" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="207" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="251" hits="0" branch="False" />
                <line number="252" hits="0" branch="False" />
                <line number="253" hits="0" branch="False" />
                <line number="254" hits="0" branch="False" />
                <line number="255" hits="0" branch="False" />
                <line number="256" hits="0" branch="False" />
                <line number="257" hits="0" branch="False" />
                <line number="258" hits="0" branch="False" />
                <line number="260" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="243" hits="0" branch="False" />
            <line number="245" hits="0" branch="False" />
            <line number="246" hits="0" branch="False" />
            <line number="248" hits="0" branch="False" />
            <line number="250" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="207" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="251" hits="0" branch="False" />
            <line number="252" hits="0" branch="False" />
            <line number="253" hits="0" branch="False" />
            <line number="254" hits="0" branch="False" />
            <line number="255" hits="0" branch="False" />
            <line number="256" hits="0" branch="False" />
            <line number="257" hits="0" branch="False" />
            <line number="258" hits="0" branch="False" />
            <line number="260" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;StartMonitoringAsync&gt;d__15" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="0" complexity="6">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="6">
              <lines>
                <line number="175" hits="0" branch="False" />
                <line number="176" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="28" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="177" hits="0" branch="False" />
                <line number="180" hits="0" branch="False" />
                <line number="181" hits="0" branch="False" />
                <line number="184" hits="0" branch="False" />
                <line number="185" hits="0" branch="False" />
                <line number="186" hits="0" branch="False" />
                <line number="187" hits="0" branch="False" />
                <line number="188" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="353" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="189" hits="0" branch="False" />
                <line number="190" hits="0" branch="False" />
                <line number="191" hits="0" branch="False" />
                <line number="192" hits="0" branch="False" />
                <line number="195" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="436" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="196" hits="0" branch="False" />
                <line number="198" hits="0" branch="False" />
                <line number="199" hits="0" branch="False" />
                <line number="200" hits="0" branch="False" />
                <line number="201" hits="0" branch="False" />
                <line number="202" hits="0" branch="False" />
                <line number="203" hits="0" branch="False" />
                <line number="205" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="175" hits="0" branch="False" />
            <line number="176" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="28" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="177" hits="0" branch="False" />
            <line number="180" hits="0" branch="False" />
            <line number="181" hits="0" branch="False" />
            <line number="184" hits="0" branch="False" />
            <line number="185" hits="0" branch="False" />
            <line number="186" hits="0" branch="False" />
            <line number="187" hits="0" branch="False" />
            <line number="188" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="353" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="189" hits="0" branch="False" />
            <line number="190" hits="0" branch="False" />
            <line number="191" hits="0" branch="False" />
            <line number="192" hits="0" branch="False" />
            <line number="195" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="436" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="196" hits="0" branch="False" />
            <line number="198" hits="0" branch="False" />
            <line number="199" hits="0" branch="False" />
            <line number="200" hits="0" branch="False" />
            <line number="201" hits="0" branch="False" />
            <line number="202" hits="0" branch="False" />
            <line number="203" hits="0" branch="False" />
            <line number="205" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortDiscovery/&lt;StopMonitoringAsync&gt;d__16" filename="Services\SerialPortDiscovery.cs" line-rate="0" branch-rate="0" complexity="4">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="211" hits="0" branch="False" />
                <line number="212" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="34" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="213" hits="0" branch="False" />
                <line number="216" hits="0" branch="False" />
                <line number="217" hits="0" branch="False" />
                <line number="220" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="82" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="221" hits="0" branch="False" />
                <line number="223" hits="0" branch="False" />
                <line number="224" hits="0" branch="False" />
                <line number="225" hits="0" branch="False" />
                <line number="226" hits="0" branch="False" />
                <line number="228" hits="0" branch="False" />
                <line number="229" hits="0" branch="False" />
                <line number="230" hits="0" branch="False" />
                <line number="231" hits="0" branch="False" />
                <line number="232" hits="0" branch="False" />
                <line number="233" hits="0" branch="False" />
                <line number="235" hits="0" branch="False" />
                <line number="236" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="211" hits="0" branch="False" />
            <line number="212" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="34" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="213" hits="0" branch="False" />
            <line number="216" hits="0" branch="False" />
            <line number="217" hits="0" branch="False" />
            <line number="220" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="82" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="221" hits="0" branch="False" />
            <line number="223" hits="0" branch="False" />
            <line number="224" hits="0" branch="False" />
            <line number="225" hits="0" branch="False" />
            <line number="226" hits="0" branch="False" />
            <line number="228" hits="0" branch="False" />
            <line number="229" hits="0" branch="False" />
            <line number="230" hits="0" branch="False" />
            <line number="231" hits="0" branch="False" />
            <line number="232" hits="0" branch="False" />
            <line number="233" hits="0" branch="False" />
            <line number="235" hits="0" branch="False" />
            <line number="236" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortService" filename="Services\SerialPortService.cs" line-rate="0" branch-rate="0" complexity="37">
          <methods>
            <method name="get_Status" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="24" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Settings" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="29" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_CurrentPort" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="34" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_IsConnected" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="39" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_AutoReconnectEnabled" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="44" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_BytesToRead" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="49" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_BytesToWrite" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="54" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ClearReceiveBuffer" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="280" hits="0" branch="False" />
                <line number="281" hits="0" branch="False" />
                <line number="282" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ClearSendBuffer" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="288" hits="0" branch="False" />
                <line number="289" hits="0" branch="False" />
                <line number="290" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnDeviceChanged" signature="(System.Object,Liam.SerialPort.Events.DeviceChangedEventArgs)" line-rate="0" branch-rate="0" complexity="10">
              <lines>
                <line number="298" hits="0" branch="False" />
                <line number="300" hits="0" branch="False" />
                <line number="301" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="304" hits="0" branch="True" condition-coverage="0% (0/8)">
                  <conditions>
                    <condition number="29" type="jump" coverage="0%" />
                    <condition number="37" type="jump" coverage="0%" />
                    <condition number="46" type="jump" coverage="0%" />
                    <condition number="78" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="305" hits="0" branch="False" />
                <line number="306" hits="0" branch="False" />
                <line number="307" hits="0" branch="False" />
                <line number="308" hits="0" branch="False" />
                <line number="309" hits="0" branch="False" />
                <line number="310" hits="0" branch="False" />
                <line number="311" hits="0" branch="False" />
                <line number="312" hits="0" branch="False" />
                <line number="313" hits="0" branch="False" />
                <line number="314" hits="0" branch="False" />
                <line number="315" hits="0" branch="False" />
                <line number="316" hits="0" branch="False" />
                <line number="317" hits="0" branch="False" />
                <line number="318" hits="0" branch="False" />
                <line number="319" hits="0" branch="False" />
                <line number="320" hits="0" branch="False" />
                <line number="321" hits="0" branch="False" />
                <line number="322" hits="0" branch="False" />
                <line number="323" hits="0" branch="False" />
                <line number="324" hits="0" branch="False" />
                <line number="325" hits="0" branch="False" />
                <line number="326" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnStatusChanged" signature="(System.Object,Liam.SerialPort.Events.ConnectionStatusChangedEventArgs)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="334" hits="0" branch="False" />
                <line number="336" hits="0" branch="False" />
                <line number="337" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="338" hits="0" branch="False" />
                <line number="339" hits="0" branch="False" />
                <line number="340" hits="0" branch="False" />
                <line number="341" hits="0" branch="False" />
                <line number="342" hits="0" branch="False" />
                <line number="343" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnDataReceived" signature="(System.Object,Liam.SerialPort.Events.DataReceivedEventArgs)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="351" hits="0" branch="False" />
                <line number="353" hits="0" branch="False" />
                <line number="354" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="355" hits="0" branch="False" />
                <line number="356" hits="0" branch="False" />
                <line number="357" hits="0" branch="False" />
                <line number="358" hits="0" branch="False" />
                <line number="359" hits="0" branch="False" />
                <line number="360" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnConnectionError" signature="(System.Object,Liam.SerialPort.Events.SerialPortErrorEventArgs)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="368" hits="0" branch="False" />
                <line number="370" hits="0" branch="False" />
                <line number="371" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="372" hits="0" branch="False" />
                <line number="373" hits="0" branch="False" />
                <line number="374" hits="0" branch="False" />
                <line number="375" hits="0" branch="False" />
                <line number="376" hits="0" branch="False" />
                <line number="377" hits="0" branch="False" />
              </lines>
            </method>
            <method name="OnDataHandlerError" signature="(System.Object,Liam.SerialPort.Events.SerialPortErrorEventArgs)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="385" hits="0" branch="False" />
                <line number="387" hits="0" branch="False" />
                <line number="388" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="389" hits="0" branch="False" />
                <line number="390" hits="0" branch="False" />
                <line number="391" hits="0" branch="False" />
                <line number="392" hits="0" branch="False" />
                <line number="393" hits="0" branch="False" />
                <line number="394" hits="0" branch="False" />
              </lines>
            </method>
            <method name="Dispose" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="400" hits="0" branch="False" />
                <line number="401" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="402" hits="0" branch="False" />
                <line number="405" hits="0" branch="False" />
                <line number="406" hits="0" branch="False" />
                <line number="409" hits="0" branch="False" />
                <line number="410" hits="0" branch="False" />
                <line number="411" hits="0" branch="False" />
                <line number="412" hits="0" branch="False" />
                <line number="413" hits="0" branch="False" />
                <line number="416" hits="0" branch="False" />
                <line number="417" hits="0" branch="False" />
                <line number="418" hits="0" branch="False" />
                <line number="419" hits="0" branch="False" />
                <line number="420" hits="0" branch="False" />
                <line number="421" hits="0" branch="False" />
                <line number="422" hits="0" branch="False" />
                <line number="423" hits="0" branch="False" />
                <line number="425" hits="0" branch="False" />
                <line number="426" hits="0" branch="False" />
                <line number="427" hits="0" branch="False" />
                <line number="428" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(Microsoft.Extensions.Logging.ILogger`1&lt;Liam.SerialPort.Services.SerialPortService&gt;,Liam.SerialPort.Interfaces.ISerialPortDiscovery,Liam.SerialPort.Interfaces.ISerialPortConnection,Liam.SerialPort.Interfaces.ISerialPortDataHandler)" line-rate="0" branch-rate="0" complexity="8">
              <lines>
                <line number="17" hits="0" branch="False" />
                <line number="83" hits="0" branch="False" />
                <line number="84" hits="0" branch="False" />
                <line number="85" hits="0" branch="False" />
                <line number="86" hits="0" branch="False" />
                <line number="87" hits="0" branch="False" />
                <line number="88" hits="0" branch="False" />
                <line number="89" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="29" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="90" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="51" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="91" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="73" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="92" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="96" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="95" hits="0" branch="False" />
                <line number="96" hits="0" branch="False" />
                <line number="97" hits="0" branch="False" />
                <line number="98" hits="0" branch="False" />
                <line number="99" hits="0" branch="False" />
                <line number="101" hits="0" branch="False" />
                <line number="102" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="24" hits="0" branch="False" />
            <line number="29" hits="0" branch="False" />
            <line number="34" hits="0" branch="False" />
            <line number="39" hits="0" branch="False" />
            <line number="44" hits="0" branch="False" />
            <line number="49" hits="0" branch="False" />
            <line number="54" hits="0" branch="False" />
            <line number="280" hits="0" branch="False" />
            <line number="281" hits="0" branch="False" />
            <line number="282" hits="0" branch="False" />
            <line number="288" hits="0" branch="False" />
            <line number="289" hits="0" branch="False" />
            <line number="290" hits="0" branch="False" />
            <line number="298" hits="0" branch="False" />
            <line number="300" hits="0" branch="False" />
            <line number="301" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="304" hits="0" branch="True" condition-coverage="0% (0/8)">
              <conditions>
                <condition number="29" type="jump" coverage="0%" />
                <condition number="37" type="jump" coverage="0%" />
                <condition number="46" type="jump" coverage="0%" />
                <condition number="78" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="305" hits="0" branch="False" />
            <line number="306" hits="0" branch="False" />
            <line number="307" hits="0" branch="False" />
            <line number="308" hits="0" branch="False" />
            <line number="309" hits="0" branch="False" />
            <line number="310" hits="0" branch="False" />
            <line number="311" hits="0" branch="False" />
            <line number="312" hits="0" branch="False" />
            <line number="313" hits="0" branch="False" />
            <line number="314" hits="0" branch="False" />
            <line number="315" hits="0" branch="False" />
            <line number="316" hits="0" branch="False" />
            <line number="317" hits="0" branch="False" />
            <line number="318" hits="0" branch="False" />
            <line number="319" hits="0" branch="False" />
            <line number="320" hits="0" branch="False" />
            <line number="321" hits="0" branch="False" />
            <line number="322" hits="0" branch="False" />
            <line number="323" hits="0" branch="False" />
            <line number="324" hits="0" branch="False" />
            <line number="325" hits="0" branch="False" />
            <line number="326" hits="0" branch="False" />
            <line number="334" hits="0" branch="False" />
            <line number="336" hits="0" branch="False" />
            <line number="337" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="338" hits="0" branch="False" />
            <line number="339" hits="0" branch="False" />
            <line number="340" hits="0" branch="False" />
            <line number="341" hits="0" branch="False" />
            <line number="342" hits="0" branch="False" />
            <line number="343" hits="0" branch="False" />
            <line number="351" hits="0" branch="False" />
            <line number="353" hits="0" branch="False" />
            <line number="354" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="355" hits="0" branch="False" />
            <line number="356" hits="0" branch="False" />
            <line number="357" hits="0" branch="False" />
            <line number="358" hits="0" branch="False" />
            <line number="359" hits="0" branch="False" />
            <line number="360" hits="0" branch="False" />
            <line number="368" hits="0" branch="False" />
            <line number="370" hits="0" branch="False" />
            <line number="371" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="372" hits="0" branch="False" />
            <line number="373" hits="0" branch="False" />
            <line number="374" hits="0" branch="False" />
            <line number="375" hits="0" branch="False" />
            <line number="376" hits="0" branch="False" />
            <line number="377" hits="0" branch="False" />
            <line number="385" hits="0" branch="False" />
            <line number="387" hits="0" branch="False" />
            <line number="388" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="389" hits="0" branch="False" />
            <line number="390" hits="0" branch="False" />
            <line number="391" hits="0" branch="False" />
            <line number="392" hits="0" branch="False" />
            <line number="393" hits="0" branch="False" />
            <line number="394" hits="0" branch="False" />
            <line number="400" hits="0" branch="False" />
            <line number="401" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="402" hits="0" branch="False" />
            <line number="405" hits="0" branch="False" />
            <line number="406" hits="0" branch="False" />
            <line number="409" hits="0" branch="False" />
            <line number="410" hits="0" branch="False" />
            <line number="411" hits="0" branch="False" />
            <line number="412" hits="0" branch="False" />
            <line number="413" hits="0" branch="False" />
            <line number="416" hits="0" branch="False" />
            <line number="417" hits="0" branch="False" />
            <line number="418" hits="0" branch="False" />
            <line number="419" hits="0" branch="False" />
            <line number="420" hits="0" branch="False" />
            <line number="421" hits="0" branch="False" />
            <line number="422" hits="0" branch="False" />
            <line number="423" hits="0" branch="False" />
            <line number="425" hits="0" branch="False" />
            <line number="426" hits="0" branch="False" />
            <line number="427" hits="0" branch="False" />
            <line number="428" hits="0" branch="False" />
            <line number="17" hits="0" branch="False" />
            <line number="83" hits="0" branch="False" />
            <line number="84" hits="0" branch="False" />
            <line number="85" hits="0" branch="False" />
            <line number="86" hits="0" branch="False" />
            <line number="87" hits="0" branch="False" />
            <line number="88" hits="0" branch="False" />
            <line number="89" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="29" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="90" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="51" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="91" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="73" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="92" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="96" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="95" hits="0" branch="False" />
            <line number="96" hits="0" branch="False" />
            <line number="97" hits="0" branch="False" />
            <line number="98" hits="0" branch="False" />
            <line number="99" hits="0" branch="False" />
            <line number="101" hits="0" branch="False" />
            <line number="102" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortService/&lt;ConnectAsync&gt;d__36" filename="Services\SerialPortService.cs" line-rate="0" branch-rate="0" complexity="14">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="14">
              <lines>
                <line number="130" hits="0" branch="False" />
                <line number="131" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="29" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="132" hits="0" branch="False" />
                <line number="134" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="58" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="135" hits="0" branch="False" />
                <line number="138" hits="0" branch="False" />
                <line number="139" hits="0" branch="False" />
                <line number="142" hits="0" branch="False" />
                <line number="144" hits="0" branch="False" />
                <line number="146" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="309" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="147" hits="0" branch="False" />
                <line number="149" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="347" type="jump" coverage="0%" />
                    <condition number="391" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="150" hits="0" branch="False" />
                <line number="151" hits="0" branch="False" />
                <line number="152" hits="0" branch="False" />
                <line number="153" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="424" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="154" hits="0" branch="False" />
                <line number="155" hits="0" branch="False" />
                <line number="156" hits="0" branch="False" />
                <line number="157" hits="0" branch="False" />
                <line number="160" hits="0" branch="False" />
                <line number="163" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="500" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="164" hits="0" branch="False" />
                <line number="165" hits="0" branch="False" />
                <line number="166" hits="0" branch="False" />
                <line number="168" hits="0" branch="False" />
                <line number="169" hits="0" branch="False" />
                <line number="171" hits="0" branch="False" />
                <line number="173" hits="0" branch="False" />
                <line number="174" hits="0" branch="False" />
                <line number="175" hits="0" branch="False" />
                <line number="176" hits="0" branch="False" />
                <line number="178" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="130" hits="0" branch="False" />
            <line number="131" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="29" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="132" hits="0" branch="False" />
            <line number="134" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="58" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="135" hits="0" branch="False" />
            <line number="138" hits="0" branch="False" />
            <line number="139" hits="0" branch="False" />
            <line number="142" hits="0" branch="False" />
            <line number="144" hits="0" branch="False" />
            <line number="146" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="309" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="147" hits="0" branch="False" />
            <line number="149" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="347" type="jump" coverage="0%" />
                <condition number="391" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="150" hits="0" branch="False" />
            <line number="151" hits="0" branch="False" />
            <line number="152" hits="0" branch="False" />
            <line number="153" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="424" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="154" hits="0" branch="False" />
            <line number="155" hits="0" branch="False" />
            <line number="156" hits="0" branch="False" />
            <line number="157" hits="0" branch="False" />
            <line number="160" hits="0" branch="False" />
            <line number="163" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="500" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="164" hits="0" branch="False" />
            <line number="165" hits="0" branch="False" />
            <line number="166" hits="0" branch="False" />
            <line number="168" hits="0" branch="False" />
            <line number="169" hits="0" branch="False" />
            <line number="171" hits="0" branch="False" />
            <line number="173" hits="0" branch="False" />
            <line number="174" hits="0" branch="False" />
            <line number="175" hits="0" branch="False" />
            <line number="176" hits="0" branch="False" />
            <line number="178" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortService/&lt;ConnectAsync&gt;d__37" filename="Services\SerialPortService.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="188" hits="0" branch="False" />
                <line number="189" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="26" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="190" hits="0" branch="False" />
                <line number="192" hits="0" branch="False" />
                <line number="193" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="188" hits="0" branch="False" />
            <line number="189" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="26" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="190" hits="0" branch="False" />
            <line number="192" hits="0" branch="False" />
            <line number="193" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortService/&lt;DisconnectAsync&gt;d__38" filename="Services\SerialPortService.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="200" hits="0" branch="False" />
                <line number="202" hits="0" branch="False" />
                <line number="203" hits="0" branch="False" />
                <line number="206" hits="0" branch="False" />
                <line number="209" hits="0" branch="False" />
                <line number="211" hits="0" branch="False" />
                <line number="212" hits="0" branch="False" />
                <line number="213" hits="0" branch="False" />
                <line number="214" hits="0" branch="False" />
                <line number="215" hits="0" branch="False" />
                <line number="216" hits="0" branch="False" />
                <line number="218" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="200" hits="0" branch="False" />
            <line number="202" hits="0" branch="False" />
            <line number="203" hits="0" branch="False" />
            <line number="206" hits="0" branch="False" />
            <line number="209" hits="0" branch="False" />
            <line number="211" hits="0" branch="False" />
            <line number="212" hits="0" branch="False" />
            <line number="213" hits="0" branch="False" />
            <line number="214" hits="0" branch="False" />
            <line number="215" hits="0" branch="False" />
            <line number="216" hits="0" branch="False" />
            <line number="218" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortService/&lt;DisposeAsync&gt;d__51" filename="Services\SerialPortService.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="434" hits="0" branch="False" />
                <line number="435" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="29" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="436" hits="0" branch="False" />
                <line number="439" hits="0" branch="False" />
                <line number="440" hits="0" branch="False" />
                <line number="443" hits="0" branch="False" />
                <line number="444" hits="0" branch="False" />
                <line number="445" hits="0" branch="False" />
                <line number="446" hits="0" branch="False" />
                <line number="447" hits="0" branch="False" />
                <line number="450" hits="0" branch="False" />
                <line number="451" hits="0" branch="False" />
                <line number="452" hits="0" branch="False" />
                <line number="453" hits="0" branch="False" />
                <line number="454" hits="0" branch="False" />
                <line number="455" hits="0" branch="False" />
                <line number="456" hits="0" branch="False" />
                <line number="457" hits="0" branch="False" />
                <line number="459" hits="0" branch="False" />
                <line number="460" hits="0" branch="False" />
                <line number="461" hits="0" branch="False" />
                <line number="462" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="434" hits="0" branch="False" />
            <line number="435" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="29" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="436" hits="0" branch="False" />
            <line number="439" hits="0" branch="False" />
            <line number="440" hits="0" branch="False" />
            <line number="443" hits="0" branch="False" />
            <line number="444" hits="0" branch="False" />
            <line number="445" hits="0" branch="False" />
            <line number="446" hits="0" branch="False" />
            <line number="447" hits="0" branch="False" />
            <line number="450" hits="0" branch="False" />
            <line number="451" hits="0" branch="False" />
            <line number="452" hits="0" branch="False" />
            <line number="453" hits="0" branch="False" />
            <line number="454" hits="0" branch="False" />
            <line number="455" hits="0" branch="False" />
            <line number="456" hits="0" branch="False" />
            <line number="457" hits="0" branch="False" />
            <line number="459" hits="0" branch="False" />
            <line number="460" hits="0" branch="False" />
            <line number="461" hits="0" branch="False" />
            <line number="462" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortService/&lt;GetAvailablePortsAsync&gt;d__35" filename="Services\SerialPortService.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="110" hits="0" branch="False" />
                <line number="112" hits="0" branch="False" />
                <line number="113" hits="0" branch="False" />
                <line number="115" hits="0" branch="False" />
                <line number="116" hits="0" branch="False" />
                <line number="117" hits="0" branch="False" />
                <line number="118" hits="0" branch="False" />
                <line number="120" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="110" hits="0" branch="False" />
            <line number="112" hits="0" branch="False" />
            <line number="113" hits="0" branch="False" />
            <line number="115" hits="0" branch="False" />
            <line number="116" hits="0" branch="False" />
            <line number="117" hits="0" branch="False" />
            <line number="118" hits="0" branch="False" />
            <line number="120" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortService/&lt;SendAndReceiveAsync&gt;d__41" filename="Services\SerialPortService.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="254" hits="0" branch="False" />
                <line number="255" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="31" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="256" hits="0" branch="False" />
                <line number="258" hits="0" branch="False" />
                <line number="259" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="254" hits="0" branch="False" />
            <line number="255" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="31" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="256" hits="0" branch="False" />
            <line number="258" hits="0" branch="False" />
            <line number="259" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortService/&lt;SendAndReceiveAsync&gt;d__42" filename="Services\SerialPortService.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="269" hits="0" branch="False" />
                <line number="270" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="31" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="271" hits="0" branch="False" />
                <line number="273" hits="0" branch="False" />
                <line number="274" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="269" hits="0" branch="False" />
            <line number="270" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="31" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="271" hits="0" branch="False" />
            <line number="273" hits="0" branch="False" />
            <line number="274" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortService/&lt;SendAsync&gt;d__39" filename="Services\SerialPortService.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="226" hits="0" branch="False" />
                <line number="227" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="31" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="228" hits="0" branch="False" />
                <line number="230" hits="0" branch="False" />
                <line number="231" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="226" hits="0" branch="False" />
            <line number="227" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="31" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="228" hits="0" branch="False" />
            <line number="230" hits="0" branch="False" />
            <line number="231" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Services.SerialPortService/&lt;SendAsync&gt;d__40" filename="Services\SerialPortService.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="239" hits="0" branch="False" />
                <line number="240" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="31" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="241" hits="0" branch="False" />
                <line number="243" hits="0" branch="False" />
                <line number="244" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="239" hits="0" branch="False" />
            <line number="240" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="31" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="241" hits="0" branch="False" />
            <line number="243" hits="0" branch="False" />
            <line number="244" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Models.ConnectionStatusExtensions" filename="Models\ConnectionStatus.cs" line-rate="0.9772" branch-rate="0.9629000000000001" complexity="30">
          <methods>
            <method name="IsConnected" signature="(Liam.SerialPort.Models.ConnectionStatus)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="60" hits="8" branch="False" />
                <line number="61" hits="8" branch="False" />
                <line number="62" hits="8" branch="False" />
              </lines>
            </method>
            <method name="IsDisconnected" signature="(Liam.SerialPort.Models.ConnectionStatus)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="70" hits="8" branch="False" />
                <line number="71" hits="8" branch="False" />
                <line number="72" hits="8" branch="False" />
              </lines>
            </method>
            <method name="IsTransitioning" signature="(Liam.SerialPort.Models.ConnectionStatus)" line-rate="1" branch-rate="1" complexity="4">
              <lines>
                <line number="80" hits="8" branch="False" />
                <line number="81" hits="8" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="3" type="jump" coverage="100%" />
                    <condition number="7" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="82" hits="8" branch="False" />
                <line number="83" hits="8" branch="False" />
                <line number="84" hits="8" branch="False" />
              </lines>
            </method>
            <method name="IsError" signature="(Liam.SerialPort.Models.ConnectionStatus)" line-rate="1" branch-rate="1" complexity="4">
              <lines>
                <line number="92" hits="8" branch="False" />
                <line number="93" hits="8" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="3" type="jump" coverage="100%" />
                    <condition number="7" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="94" hits="8" branch="False" />
                <line number="95" hits="8" branch="False" />
                <line number="96" hits="8" branch="False" />
              </lines>
            </method>
            <method name="GetDescription" signature="(Liam.SerialPort.Models.ConnectionStatus)" line-rate="0.9285" branch-rate="0.8887999999999999" complexity="9">
              <lines>
                <line number="104" hits="8" branch="False" />
                <line number="105" hits="8" branch="True" condition-coverage="88.88% (8/9)">
                  <conditions>
                    <condition number="6" type="switch" coverage="88.88%" />
                  </conditions>
                </line>
                <line number="106" hits="8" branch="False" />
                <line number="107" hits="1" branch="False" />
                <line number="108" hits="1" branch="False" />
                <line number="109" hits="1" branch="False" />
                <line number="110" hits="1" branch="False" />
                <line number="111" hits="1" branch="False" />
                <line number="112" hits="1" branch="False" />
                <line number="113" hits="1" branch="False" />
                <line number="114" hits="1" branch="False" />
                <line number="115" hits="0" branch="False" />
                <line number="116" hits="8" branch="False" />
                <line number="117" hits="8" branch="False" />
              </lines>
            </method>
            <method name="CanConnect" signature="(Liam.SerialPort.Models.ConnectionStatus)" line-rate="1" branch-rate="1" complexity="6">
              <lines>
                <line number="125" hits="8" branch="False" />
                <line number="126" hits="8" branch="True" condition-coverage="100% (6/6)">
                  <conditions>
                    <condition number="2" type="jump" coverage="100%" />
                    <condition number="6" type="jump" coverage="100%" />
                    <condition number="10" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="127" hits="8" branch="False" />
                <line number="128" hits="8" branch="False" />
                <line number="129" hits="8" branch="False" />
                <line number="130" hits="8" branch="False" />
              </lines>
            </method>
            <method name="CanDisconnect" signature="(Liam.SerialPort.Models.ConnectionStatus)" line-rate="1" branch-rate="1" complexity="4">
              <lines>
                <line number="138" hits="8" branch="False" />
                <line number="139" hits="8" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="3" type="jump" coverage="100%" />
                    <condition number="7" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="140" hits="8" branch="False" />
                <line number="141" hits="8" branch="False" />
                <line number="142" hits="8" branch="False" />
              </lines>
            </method>
            <method name="CanSendData" signature="(Liam.SerialPort.Models.ConnectionStatus)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="150" hits="8" branch="False" />
                <line number="151" hits="8" branch="False" />
                <line number="152" hits="8" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="60" hits="8" branch="False" />
            <line number="61" hits="8" branch="False" />
            <line number="62" hits="8" branch="False" />
            <line number="70" hits="8" branch="False" />
            <line number="71" hits="8" branch="False" />
            <line number="72" hits="8" branch="False" />
            <line number="80" hits="8" branch="False" />
            <line number="81" hits="8" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="3" type="jump" coverage="100%" />
                <condition number="7" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="82" hits="8" branch="False" />
            <line number="83" hits="8" branch="False" />
            <line number="84" hits="8" branch="False" />
            <line number="92" hits="8" branch="False" />
            <line number="93" hits="8" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="3" type="jump" coverage="100%" />
                <condition number="7" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="94" hits="8" branch="False" />
            <line number="95" hits="8" branch="False" />
            <line number="96" hits="8" branch="False" />
            <line number="104" hits="8" branch="False" />
            <line number="105" hits="8" branch="True" condition-coverage="88.88% (8/9)">
              <conditions>
                <condition number="6" type="switch" coverage="88.88%" />
              </conditions>
            </line>
            <line number="106" hits="8" branch="False" />
            <line number="107" hits="1" branch="False" />
            <line number="108" hits="1" branch="False" />
            <line number="109" hits="1" branch="False" />
            <line number="110" hits="1" branch="False" />
            <line number="111" hits="1" branch="False" />
            <line number="112" hits="1" branch="False" />
            <line number="113" hits="1" branch="False" />
            <line number="114" hits="1" branch="False" />
            <line number="115" hits="0" branch="False" />
            <line number="116" hits="8" branch="False" />
            <line number="117" hits="8" branch="False" />
            <line number="125" hits="8" branch="False" />
            <line number="126" hits="8" branch="True" condition-coverage="100% (6/6)">
              <conditions>
                <condition number="2" type="jump" coverage="100%" />
                <condition number="6" type="jump" coverage="100%" />
                <condition number="10" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="127" hits="8" branch="False" />
            <line number="128" hits="8" branch="False" />
            <line number="129" hits="8" branch="False" />
            <line number="130" hits="8" branch="False" />
            <line number="138" hits="8" branch="False" />
            <line number="139" hits="8" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="3" type="jump" coverage="100%" />
                <condition number="7" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="140" hits="8" branch="False" />
            <line number="141" hits="8" branch="False" />
            <line number="142" hits="8" branch="False" />
            <line number="150" hits="8" branch="False" />
            <line number="151" hits="8" branch="False" />
            <line number="152" hits="8" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Models.ConnectionStatistics" filename="Models\ConnectionStatus.cs" line-rate="1" branch-rate="1" complexity="19">
          <methods>
            <method name="get_ConnectedAt" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="163" hits="13" branch="False" />
              </lines>
            </method>
            <method name="get_LastActivity" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="168" hits="18" branch="False" />
              </lines>
            </method>
            <method name="get_Duration" signature="()" line-rate="1" branch-rate="1" complexity="2">
              <lines>
                <line number="173" hits="3" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="14" type="jump" coverage="100%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name="get_BytesSent" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="178" hits="12" branch="False" />
              </lines>
            </method>
            <method name="get_BytesReceived" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="183" hits="12" branch="False" />
              </lines>
            </method>
            <method name="get_MessagesSent" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="188" hits="12" branch="False" />
              </lines>
            </method>
            <method name="get_MessagesReceived" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="193" hits="12" branch="False" />
              </lines>
            </method>
            <method name="get_ConnectionAttempts" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="198" hits="15" branch="False" />
              </lines>
            </method>
            <method name="get_ReconnectionCount" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="203" hits="10" branch="False" />
              </lines>
            </method>
            <method name="get_ErrorCount" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="208" hits="12" branch="False" />
              </lines>
            </method>
            <method name="get_LastErrorAt" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="213" hits="9" branch="False" />
              </lines>
            </method>
            <method name="get_LastError" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="218" hits="9" branch="False" />
              </lines>
            </method>
            <method name="Reset" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="224" hits="1" branch="False" />
                <line number="225" hits="1" branch="False" />
                <line number="226" hits="1" branch="False" />
                <line number="227" hits="1" branch="False" />
                <line number="228" hits="1" branch="False" />
                <line number="229" hits="1" branch="False" />
                <line number="230" hits="1" branch="False" />
                <line number="231" hits="1" branch="False" />
                <line number="232" hits="1" branch="False" />
                <line number="233" hits="1" branch="False" />
                <line number="234" hits="1" branch="False" />
                <line number="235" hits="1" branch="False" />
                <line number="236" hits="1" branch="False" />
              </lines>
            </method>
            <method name="RecordSent" signature="(System.Int32)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="243" hits="3" branch="False" />
                <line number="244" hits="3" branch="False" />
                <line number="245" hits="3" branch="False" />
                <line number="246" hits="3" branch="False" />
                <line number="247" hits="3" branch="False" />
              </lines>
            </method>
            <method name="RecordReceived" signature="(System.Int32)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="254" hits="3" branch="False" />
                <line number="255" hits="3" branch="False" />
                <line number="256" hits="3" branch="False" />
                <line number="257" hits="3" branch="False" />
                <line number="258" hits="3" branch="False" />
              </lines>
            </method>
            <method name="RecordConnection" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="264" hits="2" branch="False" />
                <line number="265" hits="2" branch="False" />
                <line number="266" hits="2" branch="False" />
                <line number="267" hits="2" branch="False" />
                <line number="268" hits="2" branch="False" />
              </lines>
            </method>
            <method name="RecordReconnection" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="274" hits="2" branch="False" />
                <line number="275" hits="2" branch="False" />
                <line number="276" hits="2" branch="False" />
                <line number="277" hits="2" branch="False" />
              </lines>
            </method>
            <method name="RecordError" signature="(System.String)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="284" hits="3" branch="False" />
                <line number="285" hits="3" branch="False" />
                <line number="286" hits="3" branch="False" />
                <line number="287" hits="3" branch="False" />
                <line number="288" hits="3" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="163" hits="13" branch="False" />
            <line number="168" hits="18" branch="False" />
            <line number="173" hits="3" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="14" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="178" hits="12" branch="False" />
            <line number="183" hits="12" branch="False" />
            <line number="188" hits="12" branch="False" />
            <line number="193" hits="12" branch="False" />
            <line number="198" hits="15" branch="False" />
            <line number="203" hits="10" branch="False" />
            <line number="208" hits="12" branch="False" />
            <line number="213" hits="9" branch="False" />
            <line number="218" hits="9" branch="False" />
            <line number="224" hits="1" branch="False" />
            <line number="225" hits="1" branch="False" />
            <line number="226" hits="1" branch="False" />
            <line number="227" hits="1" branch="False" />
            <line number="228" hits="1" branch="False" />
            <line number="229" hits="1" branch="False" />
            <line number="230" hits="1" branch="False" />
            <line number="231" hits="1" branch="False" />
            <line number="232" hits="1" branch="False" />
            <line number="233" hits="1" branch="False" />
            <line number="234" hits="1" branch="False" />
            <line number="235" hits="1" branch="False" />
            <line number="236" hits="1" branch="False" />
            <line number="243" hits="3" branch="False" />
            <line number="244" hits="3" branch="False" />
            <line number="245" hits="3" branch="False" />
            <line number="246" hits="3" branch="False" />
            <line number="247" hits="3" branch="False" />
            <line number="254" hits="3" branch="False" />
            <line number="255" hits="3" branch="False" />
            <line number="256" hits="3" branch="False" />
            <line number="257" hits="3" branch="False" />
            <line number="258" hits="3" branch="False" />
            <line number="264" hits="2" branch="False" />
            <line number="265" hits="2" branch="False" />
            <line number="266" hits="2" branch="False" />
            <line number="267" hits="2" branch="False" />
            <line number="268" hits="2" branch="False" />
            <line number="274" hits="2" branch="False" />
            <line number="275" hits="2" branch="False" />
            <line number="276" hits="2" branch="False" />
            <line number="277" hits="2" branch="False" />
            <line number="284" hits="3" branch="False" />
            <line number="285" hits="3" branch="False" />
            <line number="286" hits="3" branch="False" />
            <line number="287" hits="3" branch="False" />
            <line number="288" hits="3" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Models.SerialPortInfo" filename="Models\SerialPortInfo.cs" line-rate="1" branch-rate="1" complexity="36">
          <methods>
            <method name="get_PortName" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="11" hits="76" branch="False" />
              </lines>
            </method>
            <method name="get_Description" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="16" hits="70" branch="False" />
              </lines>
            </method>
            <method name="get_Manufacturer" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="21" hits="54" branch="False" />
              </lines>
            </method>
            <method name="get_ProductId" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="26" hits="63" branch="False" />
              </lines>
            </method>
            <method name="get_VendorId" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="31" hits="66" branch="False" />
              </lines>
            </method>
            <method name="get_SerialNumber" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="36" hits="62" branch="False" />
              </lines>
            </method>
            <method name="get_DeviceType" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="41" hits="62" branch="False" />
              </lines>
            </method>
            <method name="get_IsAvailable" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="46" hits="54" branch="False" />
              </lines>
            </method>
            <method name="get_IsInUse" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="51" hits="54" branch="False" />
              </lines>
            </method>
            <method name="get_DevicePath" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="56" hits="61" branch="False" />
              </lines>
            </method>
            <method name="get_FriendlyName" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="61" hits="71" branch="False" />
              </lines>
            </method>
            <method name="get_DeviceInstanceId" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="66" hits="95" branch="False" />
              </lines>
            </method>
            <method name="get_HardwareIds" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="71" hits="60" branch="False" />
              </lines>
            </method>
            <method name="get_CompatibleIds" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="76" hits="57" branch="False" />
              </lines>
            </method>
            <method name="get_Properties" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="81" hits="60" branch="False" />
              </lines>
            </method>
            <method name="get_DiscoveredAt" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="86" hits="54" branch="False" />
              </lines>
            </method>
            <method name="get_LastChecked" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="91" hits="54" branch="False" />
              </lines>
            </method>
            <method name="get_DisplayName" signature="()" line-rate="1" branch-rate="1" complexity="4">
              <lines>
                <line number="96" hits="7" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="11" type="jump" coverage="100%" />
                    <condition number="24" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="97" hits="7" branch="False" />
              </lines>
            </method>
            <method name="get_UniqueId" signature="()" line-rate="1" branch-rate="1" complexity="6">
              <lines>
                <line number="102" hits="16" branch="True" condition-coverage="100% (6/6)">
                  <conditions>
                    <condition number="11" type="jump" coverage="100%" />
                    <condition number="24" type="jump" coverage="100%" />
                    <condition number="37" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="103" hits="16" branch="False" />
                <line number="104" hits="16" branch="False" />
              </lines>
            </method>
            <method name="get_IsUsbDevice" signature="()" line-rate="1" branch-rate="1" complexity="2">
              <lines>
                <line number="109" hits="6" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="11" type="jump" coverage="100%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name="get_IsVirtualPort" signature="()" line-rate="1" branch-rate="1" complexity="2">
              <lines>
                <line number="114" hits="6" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="17" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="115" hits="6" branch="False" />
              </lines>
            </method>
            <method name="ToString" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="121" hits="1" branch="False" />
              </lines>
            </method>
            <method name="Equals" signature="(System.Object)" line-rate="1" branch-rate="1" complexity="2">
              <lines>
                <line number="129" hits="6" branch="False" />
                <line number="130" hits="8" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="17" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="131" hits="4" branch="False" />
                <line number="132" hits="6" branch="False" />
              </lines>
            </method>
            <method name="GetHashCode" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="138" hits="4" branch="False" />
              </lines>
            </method>
            <method name="Clone" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="145" hits="2" branch="False" />
                <line number="146" hits="2" branch="False" />
                <line number="147" hits="2" branch="False" />
                <line number="148" hits="2" branch="False" />
                <line number="149" hits="2" branch="False" />
                <line number="150" hits="2" branch="False" />
                <line number="151" hits="2" branch="False" />
                <line number="152" hits="2" branch="False" />
                <line number="153" hits="2" branch="False" />
                <line number="154" hits="2" branch="False" />
                <line number="155" hits="2" branch="False" />
                <line number="156" hits="2" branch="False" />
                <line number="157" hits="2" branch="False" />
                <line number="158" hits="2" branch="False" />
                <line number="159" hits="2" branch="False" />
                <line number="160" hits="2" branch="False" />
                <line number="161" hits="2" branch="False" />
                <line number="162" hits="2" branch="False" />
                <line number="163" hits="2" branch="False" />
                <line number="164" hits="2" branch="False" />
                <line number="165" hits="2" branch="False" />
                <line number="166" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="11" hits="76" branch="False" />
            <line number="16" hits="70" branch="False" />
            <line number="21" hits="54" branch="False" />
            <line number="26" hits="63" branch="False" />
            <line number="31" hits="66" branch="False" />
            <line number="36" hits="62" branch="False" />
            <line number="41" hits="62" branch="False" />
            <line number="46" hits="54" branch="False" />
            <line number="51" hits="54" branch="False" />
            <line number="56" hits="61" branch="False" />
            <line number="61" hits="71" branch="False" />
            <line number="66" hits="95" branch="False" />
            <line number="71" hits="60" branch="False" />
            <line number="76" hits="57" branch="False" />
            <line number="81" hits="60" branch="False" />
            <line number="86" hits="54" branch="False" />
            <line number="91" hits="54" branch="False" />
            <line number="96" hits="7" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="11" type="jump" coverage="100%" />
                <condition number="24" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="97" hits="7" branch="False" />
            <line number="102" hits="16" branch="True" condition-coverage="100% (6/6)">
              <conditions>
                <condition number="11" type="jump" coverage="100%" />
                <condition number="24" type="jump" coverage="100%" />
                <condition number="37" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="103" hits="16" branch="False" />
            <line number="104" hits="16" branch="False" />
            <line number="109" hits="6" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="11" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="114" hits="6" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="17" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="115" hits="6" branch="False" />
            <line number="121" hits="1" branch="False" />
            <line number="129" hits="6" branch="False" />
            <line number="130" hits="8" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="17" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="131" hits="4" branch="False" />
            <line number="132" hits="6" branch="False" />
            <line number="138" hits="4" branch="False" />
            <line number="145" hits="2" branch="False" />
            <line number="146" hits="2" branch="False" />
            <line number="147" hits="2" branch="False" />
            <line number="148" hits="2" branch="False" />
            <line number="149" hits="2" branch="False" />
            <line number="150" hits="2" branch="False" />
            <line number="151" hits="2" branch="False" />
            <line number="152" hits="2" branch="False" />
            <line number="153" hits="2" branch="False" />
            <line number="154" hits="2" branch="False" />
            <line number="155" hits="2" branch="False" />
            <line number="156" hits="2" branch="False" />
            <line number="157" hits="2" branch="False" />
            <line number="158" hits="2" branch="False" />
            <line number="159" hits="2" branch="False" />
            <line number="160" hits="2" branch="False" />
            <line number="161" hits="2" branch="False" />
            <line number="162" hits="2" branch="False" />
            <line number="163" hits="2" branch="False" />
            <line number="164" hits="2" branch="False" />
            <line number="165" hits="2" branch="False" />
            <line number="166" hits="2" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Models.SerialPortSettings" filename="Models\SerialPortSettings.cs" line-rate="1" branch-rate="1" complexity="55">
          <methods>
            <method name="get_BaudRate" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="13" hits="125" branch="False" />
              </lines>
            </method>
            <method name="get_DataBits" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="18" hits="161" branch="False" />
              </lines>
            </method>
            <method name="get_StopBits" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="23" hits="73" branch="False" />
              </lines>
            </method>
            <method name="get_Parity" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="28" hits="73" branch="False" />
              </lines>
            </method>
            <method name="get_Handshake" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="33" hits="62" branch="False" />
              </lines>
            </method>
            <method name="get_ReadTimeout" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="38" hits="111" branch="False" />
              </lines>
            </method>
            <method name="get_WriteTimeout" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="43" hits="111" branch="False" />
              </lines>
            </method>
            <method name="get_ReceiveBufferSize" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="48" hits="112" branch="False" />
              </lines>
            </method>
            <method name="get_SendBufferSize" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="53" hits="112" branch="False" />
              </lines>
            </method>
            <method name="get_DtrEnable" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="58" hits="61" branch="False" />
              </lines>
            </method>
            <method name="get_RtsEnable" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="63" hits="61" branch="False" />
              </lines>
            </method>
            <method name="get_NewLine" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="68" hits="61" branch="False" />
              </lines>
            </method>
            <method name="get_Encoding" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="73" hits="61" branch="False" />
              </lines>
            </method>
            <method name="get_DiscardNull" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="78" hits="61" branch="False" />
              </lines>
            </method>
            <method name="get_RetryCount" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="83" hits="107" branch="False" />
              </lines>
            </method>
            <method name="get_RetryInterval" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="88" hits="107" branch="False" />
              </lines>
            </method>
            <method name="get_AutoReconnect" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="93" hits="61" branch="False" />
              </lines>
            </method>
            <method name="get_AutoReconnectInterval" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="98" hits="107" branch="False" />
              </lines>
            </method>
            <method name="get_MaxAutoReconnectAttempts" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="103" hits="61" branch="False" />
              </lines>
            </method>
            <method name="get_ConnectionTimeout" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="108" hits="107" branch="False" />
              </lines>
            </method>
            <method name="get_HeartbeatInterval" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="113" hits="107" branch="False" />
              </lines>
            </method>
            <method name="get_HeartbeatData" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="118" hits="64" branch="False" />
              </lines>
            </method>
            <method name="get_EnableDataLogging" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="123" hits="61" branch="False" />
              </lines>
            </method>
            <method name="get_MaxLogSize" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="128" hits="107" branch="False" />
              </lines>
            </method>
            <method name="get_Default" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="134" hits="1" branch="False" />
              </lines>
            </method>
            <method name="get_Baud9600" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="140" hits="1" branch="False" />
              </lines>
            </method>
            <method name="get_Baud115200" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="146" hits="1" branch="False" />
              </lines>
            </method>
            <method name="Validate" signature="()" line-rate="1" branch-rate="1" complexity="26">
              <lines>
                <line number="153" hits="41" branch="False" />
                <line number="154" hits="41" branch="False" />
                <line number="156" hits="41" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="21" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="157" hits="3" branch="False" />
                <line number="159" hits="41" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="42" type="jump" coverage="100%" />
                    <condition number="58" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="160" hits="3" branch="False" />
                <line number="162" hits="41" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="83" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="163" hits="2" branch="False" />
                <line number="165" hits="41" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="110" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="166" hits="2" branch="False" />
                <line number="168" hits="41" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="140" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="169" hits="3" branch="False" />
                <line number="171" hits="41" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="170" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="172" hits="3" branch="False" />
                <line number="174" hits="41" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="197" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="175" hits="2" branch="False" />
                <line number="177" hits="41" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="224" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="178" hits="2" branch="False" />
                <line number="180" hits="41" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="251" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="181" hits="2" branch="False" />
                <line number="183" hits="41" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="281" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="184" hits="3" branch="False" />
                <line number="186" hits="41" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="308" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="187" hits="2" branch="False" />
                <line number="189" hits="41" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="339" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="190" hits="3" branch="False" />
                <line number="192" hits="41" branch="False" />
                <line number="193" hits="41" branch="False" />
              </lines>
            </method>
            <method name="Clone" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="200" hits="1" branch="False" />
                <line number="201" hits="1" branch="False" />
                <line number="202" hits="1" branch="False" />
                <line number="203" hits="1" branch="False" />
                <line number="204" hits="1" branch="False" />
                <line number="205" hits="1" branch="False" />
                <line number="206" hits="1" branch="False" />
                <line number="207" hits="1" branch="False" />
                <line number="208" hits="1" branch="False" />
                <line number="209" hits="1" branch="False" />
                <line number="210" hits="1" branch="False" />
                <line number="211" hits="1" branch="False" />
                <line number="212" hits="1" branch="False" />
                <line number="213" hits="1" branch="False" />
                <line number="214" hits="1" branch="False" />
                <line number="215" hits="1" branch="False" />
                <line number="216" hits="1" branch="False" />
                <line number="217" hits="1" branch="False" />
                <line number="218" hits="1" branch="False" />
                <line number="219" hits="1" branch="False" />
                <line number="220" hits="1" branch="False" />
                <line number="221" hits="1" branch="False" />
                <line number="222" hits="1" branch="False" />
                <line number="223" hits="1" branch="False" />
                <line number="224" hits="1" branch="False" />
                <line number="225" hits="1" branch="False" />
                <line number="226" hits="1" branch="False" />
                <line number="227" hits="1" branch="False" />
                <line number="228" hits="1" branch="False" />
              </lines>
            </method>
            <method name="ToString" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="235" hits="1" branch="False" />
                <line number="236" hits="1" branch="False" />
                <line number="237" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="13" hits="125" branch="False" />
            <line number="18" hits="161" branch="False" />
            <line number="23" hits="73" branch="False" />
            <line number="28" hits="73" branch="False" />
            <line number="33" hits="62" branch="False" />
            <line number="38" hits="111" branch="False" />
            <line number="43" hits="111" branch="False" />
            <line number="48" hits="112" branch="False" />
            <line number="53" hits="112" branch="False" />
            <line number="58" hits="61" branch="False" />
            <line number="63" hits="61" branch="False" />
            <line number="68" hits="61" branch="False" />
            <line number="73" hits="61" branch="False" />
            <line number="78" hits="61" branch="False" />
            <line number="83" hits="107" branch="False" />
            <line number="88" hits="107" branch="False" />
            <line number="93" hits="61" branch="False" />
            <line number="98" hits="107" branch="False" />
            <line number="103" hits="61" branch="False" />
            <line number="108" hits="107" branch="False" />
            <line number="113" hits="107" branch="False" />
            <line number="118" hits="64" branch="False" />
            <line number="123" hits="61" branch="False" />
            <line number="128" hits="107" branch="False" />
            <line number="134" hits="1" branch="False" />
            <line number="140" hits="1" branch="False" />
            <line number="146" hits="1" branch="False" />
            <line number="153" hits="41" branch="False" />
            <line number="154" hits="41" branch="False" />
            <line number="156" hits="41" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="21" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="157" hits="3" branch="False" />
            <line number="159" hits="41" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="42" type="jump" coverage="100%" />
                <condition number="58" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="160" hits="3" branch="False" />
            <line number="162" hits="41" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="83" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="163" hits="2" branch="False" />
            <line number="165" hits="41" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="110" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="166" hits="2" branch="False" />
            <line number="168" hits="41" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="140" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="169" hits="3" branch="False" />
            <line number="171" hits="41" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="170" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="172" hits="3" branch="False" />
            <line number="174" hits="41" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="197" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="175" hits="2" branch="False" />
            <line number="177" hits="41" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="224" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="178" hits="2" branch="False" />
            <line number="180" hits="41" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="251" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="181" hits="2" branch="False" />
            <line number="183" hits="41" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="281" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="184" hits="3" branch="False" />
            <line number="186" hits="41" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="308" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="187" hits="2" branch="False" />
            <line number="189" hits="41" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="339" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="190" hits="3" branch="False" />
            <line number="192" hits="41" branch="False" />
            <line number="193" hits="41" branch="False" />
            <line number="200" hits="1" branch="False" />
            <line number="201" hits="1" branch="False" />
            <line number="202" hits="1" branch="False" />
            <line number="203" hits="1" branch="False" />
            <line number="204" hits="1" branch="False" />
            <line number="205" hits="1" branch="False" />
            <line number="206" hits="1" branch="False" />
            <line number="207" hits="1" branch="False" />
            <line number="208" hits="1" branch="False" />
            <line number="209" hits="1" branch="False" />
            <line number="210" hits="1" branch="False" />
            <line number="211" hits="1" branch="False" />
            <line number="212" hits="1" branch="False" />
            <line number="213" hits="1" branch="False" />
            <line number="214" hits="1" branch="False" />
            <line number="215" hits="1" branch="False" />
            <line number="216" hits="1" branch="False" />
            <line number="217" hits="1" branch="False" />
            <line number="218" hits="1" branch="False" />
            <line number="219" hits="1" branch="False" />
            <line number="220" hits="1" branch="False" />
            <line number="221" hits="1" branch="False" />
            <line number="222" hits="1" branch="False" />
            <line number="223" hits="1" branch="False" />
            <line number="224" hits="1" branch="False" />
            <line number="225" hits="1" branch="False" />
            <line number="226" hits="1" branch="False" />
            <line number="227" hits="1" branch="False" />
            <line number="228" hits="1" branch="False" />
            <line number="235" hits="1" branch="False" />
            <line number="236" hits="1" branch="False" />
            <line number="237" hits="1" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Models.ValidationResult" filename="Models\SerialPortSettings.cs" line-rate="1" branch-rate="1" complexity="3">
          <methods>
            <method name="get_IsValid" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="248" hits="41" branch="False" />
              </lines>
            </method>
            <method name="get_Errors" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="253" hits="14" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.Boolean,System.Collections.Generic.IEnumerable`1&lt;System.String&gt;)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="260" hits="41" branch="False" />
                <line number="261" hits="41" branch="False" />
                <line number="262" hits="41" branch="False" />
                <line number="263" hits="41" branch="False" />
                <line number="264" hits="41" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="248" hits="41" branch="False" />
            <line number="253" hits="14" branch="False" />
            <line number="260" hits="41" branch="False" />
            <line number="261" hits="41" branch="False" />
            <line number="262" hits="41" branch="False" />
            <line number="263" hits="41" branch="False" />
            <line number="264" hits="41" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortExtensions" filename="Extensions\SerialPortExtensions.cs" line-rate="0.9921" branch-rate="0.9137000000000001" complexity="65">
          <methods>
            <method name="IsUsbDevice" signature="(Liam.SerialPort.Models.SerialPortInfo)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="152" hits="2" branch="False" />
                <line number="153" hits="2" branch="False" />
                <line number="154" hits="2" branch="False" />
              </lines>
            </method>
            <method name="IsVirtualPort" signature="(Liam.SerialPort.Models.SerialPortInfo)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="162" hits="2" branch="False" />
                <line number="163" hits="2" branch="False" />
                <line number="164" hits="2" branch="False" />
              </lines>
            </method>
            <method name="GetDisplayName" signature="(Liam.SerialPort.Models.SerialPortInfo)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="172" hits="1" branch="False" />
                <line number="173" hits="1" branch="False" />
                <line number="174" hits="1" branch="False" />
              </lines>
            </method>
            <method name="CreateSettings" signature="(System.Int32,System.Int32,System.IO.Ports.StopBits,System.IO.Ports.Parity)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="185" hits="3" branch="False" />
                <line number="186" hits="3" branch="False" />
                <line number="187" hits="3" branch="False" />
                <line number="188" hits="3" branch="False" />
                <line number="189" hits="3" branch="False" />
                <line number="190" hits="3" branch="False" />
                <line number="191" hits="3" branch="False" />
                <line number="192" hits="3" branch="False" />
                <line number="193" hits="3" branch="False" />
              </lines>
            </method>
            <method name="CreateHighSpeedSettings" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="200" hits="1" branch="False" />
                <line number="201" hits="1" branch="False" />
                <line number="202" hits="1" branch="False" />
                <line number="203" hits="1" branch="False" />
                <line number="204" hits="1" branch="False" />
                <line number="205" hits="1" branch="False" />
                <line number="206" hits="1" branch="False" />
                <line number="207" hits="1" branch="False" />
                <line number="208" hits="1" branch="False" />
                <line number="209" hits="1" branch="False" />
                <line number="210" hits="1" branch="False" />
                <line number="211" hits="1" branch="False" />
                <line number="212" hits="1" branch="False" />
              </lines>
            </method>
            <method name="CreateLowSpeedSettings" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="219" hits="1" branch="False" />
                <line number="220" hits="1" branch="False" />
                <line number="221" hits="1" branch="False" />
                <line number="222" hits="1" branch="False" />
                <line number="223" hits="1" branch="False" />
                <line number="224" hits="1" branch="False" />
                <line number="225" hits="1" branch="False" />
                <line number="226" hits="1" branch="False" />
                <line number="227" hits="1" branch="False" />
                <line number="228" hits="1" branch="False" />
                <line number="229" hits="1" branch="False" />
                <line number="230" hits="1" branch="False" />
                <line number="231" hits="1" branch="False" />
              </lines>
            </method>
            <method name="IsValidBaudRate" signature="(System.Int32)" line-rate="1" branch-rate="1" complexity="2">
              <lines>
                <line number="239" hits="8" branch="False" />
                <line number="240" hits="8" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="4" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="241" hits="8" branch="False" />
                <line number="242" hits="8" branch="False" />
              </lines>
            </method>
            <method name="GetRecommendedBaudRates" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="249" hits="1" branch="False" />
                <line number="250" hits="1" branch="False" />
                <line number="251" hits="1" branch="False" />
              </lines>
            </method>
            <method name="ToHexString" signature="(System.Byte[],System.String)" line-rate="1" branch-rate="1" complexity="4">
              <lines>
                <line number="260" hits="6" branch="False" />
                <line number="261" hits="6" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="2" type="jump" coverage="100%" />
                    <condition number="14" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="262" hits="2" branch="False" />
                <line number="264" hits="21" branch="False" />
                <line number="265" hits="6" branch="False" />
              </lines>
            </method>
            <method name="HexStringToBytes" signature="(System.String)" line-rate="1" branch-rate="1" complexity="8">
              <lines>
                <line number="273" hits="14" branch="False" />
                <line number="274" hits="14" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="275" hits="4" branch="False" />
                <line number="278" hits="10" branch="False" />
                <line number="279" hits="110" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="72" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="280" hits="40" branch="False" />
                <line number="281" hits="40" branch="False" />
                <line number="282" hits="40" branch="False" />
                <line number="284" hits="10" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="89" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="285" hits="1" branch="False" />
                <line number="287" hits="9" branch="False" />
                <line number="288" hits="72" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="167" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="289" hits="29" branch="False" />
                <line number="290" hits="29" branch="False" />
                <line number="291" hits="27" branch="False" />
                <line number="293" hits="7" branch="False" />
                <line number="294" hits="11" branch="False" />
              </lines>
            </method>
            <method name="ContainsPattern" signature="(System.Byte[],System.Byte[])" line-rate="1" branch-rate="1" complexity="16">
              <lines>
                <line number="303" hits="9" branch="False" />
                <line number="304" hits="9" branch="True" condition-coverage="100% (8/8)">
                  <conditions>
                    <condition number="2" type="jump" coverage="100%" />
                    <condition number="5" type="jump" coverage="100%" />
                    <condition number="9" type="jump" coverage="100%" />
                    <condition number="24" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="305" hits="5" branch="False" />
                <line number="307" hits="14" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="122" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="308" hits="6" branch="False" />
                <line number="309" hits="6" branch="False" />
                <line number="310" hits="28" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="87" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="311" hits="11" branch="False" />
                <line number="312" hits="11" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="62" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="313" hits="3" branch="False" />
                <line number="314" hits="3" branch="False" />
                <line number="315" hits="3" branch="False" />
                <line number="317" hits="8" branch="False" />
                <line number="318" hits="6" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="94" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="319" hits="3" branch="False" />
                <line number="320" hits="3" branch="False" />
                <line number="322" hits="1" branch="False" />
                <line number="323" hits="9" branch="False" />
              </lines>
            </method>
            <method name="FindPattern" signature="(System.Byte[],System.Byte[])" line-rate="1" branch-rate="0.8125" complexity="16">
              <lines>
                <line number="332" hits="5" branch="False" />
                <line number="333" hits="5" branch="True" condition-coverage="62.5% (5/8)">
                  <conditions>
                    <condition number="2" type="jump" coverage="50%" />
                    <condition number="5" type="jump" coverage="50%" />
                    <condition number="9" type="jump" coverage="50%" />
                    <condition number="24" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="334" hits="1" branch="False" />
                <line number="336" hits="14" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="122" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="337" hits="6" branch="False" />
                <line number="338" hits="6" branch="False" />
                <line number="339" hits="28" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="87" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="340" hits="11" branch="False" />
                <line number="341" hits="11" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="62" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="342" hits="3" branch="False" />
                <line number="343" hits="3" branch="False" />
                <line number="344" hits="3" branch="False" />
                <line number="346" hits="8" branch="False" />
                <line number="347" hits="6" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="94" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="348" hits="3" branch="False" />
                <line number="349" hits="3" branch="False" />
                <line number="351" hits="1" branch="False" />
                <line number="352" hits="5" branch="False" />
              </lines>
            </method>
            <method name="CalculateChecksum" signature="(System.Byte[],Liam.SerialPort.Extensions.ChecksumType)" line-rate="0.909" branch-rate="0.75" complexity="8">
              <lines>
                <line number="361" hits="9" branch="False" />
                <line number="362" hits="9" branch="True" condition-coverage="75% (3/4)">
                  <conditions>
                    <condition number="2" type="jump" coverage="50%" />
                    <condition number="14" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="363" hits="1" branch="False" />
                <line number="365" hits="8" branch="True" condition-coverage="75% (3/4)">
                  <conditions>
                    <condition number="28" type="switch" coverage="75%" />
                  </conditions>
                </line>
                <line number="366" hits="8" branch="False" />
                <line number="367" hits="15" branch="False" />
                <line number="368" hits="8" branch="False" />
                <line number="369" hits="8" branch="False" />
                <line number="370" hits="0" branch="False" />
                <line number="371" hits="8" branch="False" />
                <line number="372" hits="9" branch="False" />
              </lines>
            </method>
            <method name="VerifyChecksum" signature="(System.Byte[],Liam.SerialPort.Extensions.ChecksumType)" line-rate="1" branch-rate="1" complexity="4">
              <lines>
                <line number="381" hits="8" branch="False" />
                <line number="382" hits="8" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="2" type="jump" coverage="100%" />
                    <condition number="15" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="383" hits="4" branch="False" />
                <line number="385" hits="4" branch="False" />
                <line number="386" hits="4" branch="False" />
                <line number="387" hits="4" branch="False" />
                <line number="389" hits="4" branch="False" />
                <line number="390" hits="8" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="152" hits="2" branch="False" />
            <line number="153" hits="2" branch="False" />
            <line number="154" hits="2" branch="False" />
            <line number="162" hits="2" branch="False" />
            <line number="163" hits="2" branch="False" />
            <line number="164" hits="2" branch="False" />
            <line number="172" hits="1" branch="False" />
            <line number="173" hits="1" branch="False" />
            <line number="174" hits="1" branch="False" />
            <line number="185" hits="3" branch="False" />
            <line number="186" hits="3" branch="False" />
            <line number="187" hits="3" branch="False" />
            <line number="188" hits="3" branch="False" />
            <line number="189" hits="3" branch="False" />
            <line number="190" hits="3" branch="False" />
            <line number="191" hits="3" branch="False" />
            <line number="192" hits="3" branch="False" />
            <line number="193" hits="3" branch="False" />
            <line number="200" hits="1" branch="False" />
            <line number="201" hits="1" branch="False" />
            <line number="202" hits="1" branch="False" />
            <line number="203" hits="1" branch="False" />
            <line number="204" hits="1" branch="False" />
            <line number="205" hits="1" branch="False" />
            <line number="206" hits="1" branch="False" />
            <line number="207" hits="1" branch="False" />
            <line number="208" hits="1" branch="False" />
            <line number="209" hits="1" branch="False" />
            <line number="210" hits="1" branch="False" />
            <line number="211" hits="1" branch="False" />
            <line number="212" hits="1" branch="False" />
            <line number="219" hits="1" branch="False" />
            <line number="220" hits="1" branch="False" />
            <line number="221" hits="1" branch="False" />
            <line number="222" hits="1" branch="False" />
            <line number="223" hits="1" branch="False" />
            <line number="224" hits="1" branch="False" />
            <line number="225" hits="1" branch="False" />
            <line number="226" hits="1" branch="False" />
            <line number="227" hits="1" branch="False" />
            <line number="228" hits="1" branch="False" />
            <line number="229" hits="1" branch="False" />
            <line number="230" hits="1" branch="False" />
            <line number="231" hits="1" branch="False" />
            <line number="239" hits="8" branch="False" />
            <line number="240" hits="8" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="4" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="241" hits="8" branch="False" />
            <line number="242" hits="8" branch="False" />
            <line number="249" hits="1" branch="False" />
            <line number="250" hits="1" branch="False" />
            <line number="251" hits="1" branch="False" />
            <line number="260" hits="6" branch="False" />
            <line number="261" hits="6" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="2" type="jump" coverage="100%" />
                <condition number="14" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="262" hits="2" branch="False" />
            <line number="264" hits="21" branch="False" />
            <line number="265" hits="6" branch="False" />
            <line number="273" hits="14" branch="False" />
            <line number="274" hits="14" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="9" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="275" hits="4" branch="False" />
            <line number="278" hits="10" branch="False" />
            <line number="279" hits="110" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="72" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="280" hits="40" branch="False" />
            <line number="281" hits="40" branch="False" />
            <line number="282" hits="40" branch="False" />
            <line number="284" hits="10" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="89" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="285" hits="1" branch="False" />
            <line number="287" hits="9" branch="False" />
            <line number="288" hits="72" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="167" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="289" hits="29" branch="False" />
            <line number="290" hits="29" branch="False" />
            <line number="291" hits="27" branch="False" />
            <line number="293" hits="7" branch="False" />
            <line number="294" hits="11" branch="False" />
            <line number="303" hits="9" branch="False" />
            <line number="304" hits="9" branch="True" condition-coverage="100% (8/8)">
              <conditions>
                <condition number="2" type="jump" coverage="100%" />
                <condition number="5" type="jump" coverage="100%" />
                <condition number="9" type="jump" coverage="100%" />
                <condition number="24" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="305" hits="5" branch="False" />
            <line number="307" hits="14" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="122" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="308" hits="6" branch="False" />
            <line number="309" hits="6" branch="False" />
            <line number="310" hits="28" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="87" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="311" hits="11" branch="False" />
            <line number="312" hits="11" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="62" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="313" hits="3" branch="False" />
            <line number="314" hits="3" branch="False" />
            <line number="315" hits="3" branch="False" />
            <line number="317" hits="8" branch="False" />
            <line number="318" hits="6" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="94" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="319" hits="3" branch="False" />
            <line number="320" hits="3" branch="False" />
            <line number="322" hits="1" branch="False" />
            <line number="323" hits="9" branch="False" />
            <line number="332" hits="5" branch="False" />
            <line number="333" hits="5" branch="True" condition-coverage="62.5% (5/8)">
              <conditions>
                <condition number="2" type="jump" coverage="50%" />
                <condition number="5" type="jump" coverage="50%" />
                <condition number="9" type="jump" coverage="50%" />
                <condition number="24" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="334" hits="1" branch="False" />
            <line number="336" hits="14" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="122" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="337" hits="6" branch="False" />
            <line number="338" hits="6" branch="False" />
            <line number="339" hits="28" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="87" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="340" hits="11" branch="False" />
            <line number="341" hits="11" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="62" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="342" hits="3" branch="False" />
            <line number="343" hits="3" branch="False" />
            <line number="344" hits="3" branch="False" />
            <line number="346" hits="8" branch="False" />
            <line number="347" hits="6" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="94" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="348" hits="3" branch="False" />
            <line number="349" hits="3" branch="False" />
            <line number="351" hits="1" branch="False" />
            <line number="352" hits="5" branch="False" />
            <line number="361" hits="9" branch="False" />
            <line number="362" hits="9" branch="True" condition-coverage="75% (3/4)">
              <conditions>
                <condition number="2" type="jump" coverage="50%" />
                <condition number="14" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="363" hits="1" branch="False" />
            <line number="365" hits="8" branch="True" condition-coverage="75% (3/4)">
              <conditions>
                <condition number="28" type="switch" coverage="75%" />
              </conditions>
            </line>
            <line number="366" hits="8" branch="False" />
            <line number="367" hits="15" branch="False" />
            <line number="368" hits="8" branch="False" />
            <line number="369" hits="8" branch="False" />
            <line number="370" hits="0" branch="False" />
            <line number="371" hits="8" branch="False" />
            <line number="372" hits="9" branch="False" />
            <line number="381" hits="8" branch="False" />
            <line number="382" hits="8" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="2" type="jump" coverage="100%" />
                <condition number="15" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="383" hits="4" branch="False" />
            <line number="385" hits="4" branch="False" />
            <line number="386" hits="4" branch="False" />
            <line number="387" hits="4" branch="False" />
            <line number="389" hits="4" branch="False" />
            <line number="390" hits="8" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortExtensions/&lt;&gt;c__DisplayClass4_0" filename="Extensions\SerialPortExtensions.cs" line-rate="0" branch-rate="0" complexity="4">
          <methods>
            <method name="&lt;WaitForDataAsync&gt;g__OnDataReceived|0" signature="(System.Object,Liam.SerialPort.Events.DataReceivedEventArgs)" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="100" hits="0" branch="False" />
                <line number="101" hits="0" branch="False" />
                <line number="103" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="45" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="104" hits="0" branch="False" />
                <line number="105" hits="0" branch="False" />
                <line number="106" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="99" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="107" hits="0" branch="False" />
                <line number="108" hits="0" branch="False" />
                <line number="109" hits="0" branch="False" />
                <line number="110" hits="0" branch="False" />
                <line number="111" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="100" hits="0" branch="False" />
            <line number="101" hits="0" branch="False" />
            <line number="103" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="45" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="104" hits="0" branch="False" />
            <line number="105" hits="0" branch="False" />
            <line number="106" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="99" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="107" hits="0" branch="False" />
            <line number="108" hits="0" branch="False" />
            <line number="109" hits="0" branch="False" />
            <line number="110" hits="0" branch="False" />
            <line number="111" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortExtensions/&lt;SendBatchAsync&gt;d__2" filename="Extensions\SerialPortExtensions.cs" line-rate="0" branch-rate="0" complexity="4">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="49" hits="0" branch="False" />
                <line number="50" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="352" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="51" hits="0" branch="False" />
                <line number="52" hits="0" branch="False" />
                <line number="54" hits="0" branch="False" />
                <line number="56" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="219" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="57" hits="0" branch="False" />
                <line number="58" hits="0" branch="False" />
                <line number="59" hits="0" branch="False" />
                <line number="60" hits="0" branch="False" />
                <line number="61" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="49" hits="0" branch="False" />
            <line number="50" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="352" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="51" hits="0" branch="False" />
            <line number="52" hits="0" branch="False" />
            <line number="54" hits="0" branch="False" />
            <line number="56" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="219" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="57" hits="0" branch="False" />
            <line number="58" hits="0" branch="False" />
            <line number="59" hits="0" branch="False" />
            <line number="60" hits="0" branch="False" />
            <line number="61" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortExtensions/&lt;SendBatchAsync&gt;d__3" filename="Extensions\SerialPortExtensions.cs" line-rate="0" branch-rate="0" complexity="4">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="71" hits="0" branch="False" />
                <line number="72" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="352" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="73" hits="0" branch="False" />
                <line number="74" hits="0" branch="False" />
                <line number="76" hits="0" branch="False" />
                <line number="78" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="219" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="79" hits="0" branch="False" />
                <line number="80" hits="0" branch="False" />
                <line number="81" hits="0" branch="False" />
                <line number="82" hits="0" branch="False" />
                <line number="83" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="71" hits="0" branch="False" />
            <line number="72" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="352" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="73" hits="0" branch="False" />
            <line number="74" hits="0" branch="False" />
            <line number="76" hits="0" branch="False" />
            <line number="78" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="219" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="79" hits="0" branch="False" />
            <line number="80" hits="0" branch="False" />
            <line number="81" hits="0" branch="False" />
            <line number="82" hits="0" branch="False" />
            <line number="83" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortExtensions/&lt;SendHexAsync&gt;d__0" filename="Extensions\SerialPortExtensions.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="20" hits="0" branch="False" />
                <line number="21" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="28" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="22" hits="0" branch="False" />
                <line number="24" hits="0" branch="False" />
                <line number="25" hits="0" branch="False" />
                <line number="26" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="20" hits="0" branch="False" />
            <line number="21" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="28" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="22" hits="0" branch="False" />
            <line number="24" hits="0" branch="False" />
            <line number="25" hits="0" branch="False" />
            <line number="26" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortExtensions/&lt;SendLineAsync&gt;d__1" filename="Extensions\SerialPortExtensions.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="36" hits="0" branch="False" />
                <line number="37" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="21" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="38" hits="0" branch="False" />
                <line number="39" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="36" hits="0" branch="False" />
            <line number="37" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="21" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="38" hits="0" branch="False" />
            <line number="39" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortExtensions/&lt;WaitForDataAsync&gt;d__4" filename="Extensions\SerialPortExtensions.cs" line-rate="0" branch-rate="0" complexity="6">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="6">
              <lines>
                <line number="94" hits="0" branch="False" />
                <line number="95" hits="0" branch="False" />
                <line number="96" hits="0" branch="False" />
                <line number="97" hits="0" branch="False" />
                <line number="114" hits="0" branch="False" />
                <line number="115" hits="0" branch="False" />
                <line number="117" hits="0" branch="True" condition-coverage="0% (0/6)">
                  <conditions>
                    <condition number="295" type="jump" coverage="0%" />
                    <condition number="259" type="jump" coverage="0%" />
                    <condition number="272" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="118" hits="0" branch="False" />
                <line number="119" hits="0" branch="False" />
                <line number="120" hits="0" branch="False" />
                <line number="122" hits="0" branch="False" />
                <line number="125" hits="0" branch="False" />
                <line number="126" hits="0" branch="False" />
                <line number="127" hits="0" branch="False" />
                <line number="128" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="94" hits="0" branch="False" />
            <line number="95" hits="0" branch="False" />
            <line number="96" hits="0" branch="False" />
            <line number="97" hits="0" branch="False" />
            <line number="114" hits="0" branch="False" />
            <line number="115" hits="0" branch="False" />
            <line number="117" hits="0" branch="True" condition-coverage="0% (0/6)">
              <conditions>
                <condition number="295" type="jump" coverage="0%" />
                <condition number="259" type="jump" coverage="0%" />
                <condition number="272" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="118" hits="0" branch="False" />
            <line number="119" hits="0" branch="False" />
            <line number="120" hits="0" branch="False" />
            <line number="122" hits="0" branch="False" />
            <line number="125" hits="0" branch="False" />
            <line number="126" hits="0" branch="False" />
            <line number="127" hits="0" branch="False" />
            <line number="128" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortExtensions/&lt;WaitForStringAsync&gt;d__5" filename="Extensions\SerialPortExtensions.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="140" hits="0" branch="False" />
                <line number="141" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="21" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="142" hits="0" branch="False" />
                <line number="143" hits="0" branch="False" />
                <line number="144" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="140" hits="0" branch="False" />
            <line number="141" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="21" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="142" hits="0" branch="False" />
            <line number="143" hits="0" branch="False" />
            <line number="144" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.ServiceCollectionExtensions" filename="Extensions\ServiceCollectionExtensions.cs" line-rate="0" branch-rate="0" complexity="7">
          <methods>
            <method name="AddSerialPort" signature="(Microsoft.Extensions.DependencyInjection.IServiceCollection)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="20" hits="0" branch="False" />
                <line number="22" hits="0" branch="False" />
                <line number="23" hits="0" branch="False" />
                <line number="24" hits="0" branch="False" />
                <line number="25" hits="0" branch="False" />
                <line number="27" hits="0" branch="False" />
                <line number="28" hits="0" branch="False" />
              </lines>
            </method>
            <method name="AddSerialPortSingleton" signature="(Microsoft.Extensions.DependencyInjection.IServiceCollection)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="36" hits="0" branch="False" />
                <line number="38" hits="0" branch="False" />
                <line number="39" hits="0" branch="False" />
                <line number="40" hits="0" branch="False" />
                <line number="41" hits="0" branch="False" />
                <line number="43" hits="0" branch="False" />
                <line number="44" hits="0" branch="False" />
              </lines>
            </method>
            <method name="AddSerialPortScoped" signature="(Microsoft.Extensions.DependencyInjection.IServiceCollection)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="52" hits="0" branch="False" />
                <line number="54" hits="0" branch="False" />
                <line number="55" hits="0" branch="False" />
                <line number="56" hits="0" branch="False" />
                <line number="57" hits="0" branch="False" />
                <line number="59" hits="0" branch="False" />
                <line number="60" hits="0" branch="False" />
              </lines>
            </method>
            <method name="AddSerialPortFactory" signature="(Microsoft.Extensions.DependencyInjection.IServiceCollection)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="68" hits="0" branch="False" />
                <line number="70" hits="0" branch="False" />
                <line number="73" hits="0" branch="False" />
                <line number="74" hits="0" branch="False" />
                <line number="75" hits="0" branch="False" />
                <line number="76" hits="0" branch="False" />
                <line number="77" hits="0" branch="False" />
                <line number="78" hits="0" branch="False" />
                <line number="79" hits="0" branch="False" />
                <line number="80" hits="0" branch="False" />
                <line number="81" hits="0" branch="False" />
                <line number="82" hits="0" branch="False" />
                <line number="83" hits="0" branch="False" />
                <line number="84" hits="0" branch="False" />
                <line number="85" hits="0" branch="False" />
                <line number="86" hits="0" branch="False" />
                <line number="88" hits="0" branch="False" />
                <line number="89" hits="0" branch="False" />
              </lines>
            </method>
            <method name="AddSerialPortPool" signature="(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Int32)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="98" hits="0" branch="False" />
                <line number="99" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="28" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="100" hits="0" branch="False" />
                <line number="103" hits="0" branch="False" />
                <line number="106" hits="0" branch="False" />
                <line number="107" hits="0" branch="False" />
                <line number="108" hits="0" branch="False" />
                <line number="109" hits="0" branch="False" />
                <line number="110" hits="0" branch="False" />
                <line number="111" hits="0" branch="False" />
                <line number="112" hits="0" branch="False" />
                <line number="114" hits="0" branch="False" />
                <line number="115" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ConfigureSerialPortLogging" signature="(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action`1&lt;Microsoft.Extensions.Logging.ILoggingBuilder&gt;)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="124" hits="0" branch="False" />
                <line number="127" hits="0" branch="False" />
                <line number="128" hits="0" branch="False" />
                <line number="129" hits="0" branch="False" />
                <line number="130" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="20" hits="0" branch="False" />
            <line number="22" hits="0" branch="False" />
            <line number="23" hits="0" branch="False" />
            <line number="24" hits="0" branch="False" />
            <line number="25" hits="0" branch="False" />
            <line number="27" hits="0" branch="False" />
            <line number="28" hits="0" branch="False" />
            <line number="36" hits="0" branch="False" />
            <line number="38" hits="0" branch="False" />
            <line number="39" hits="0" branch="False" />
            <line number="40" hits="0" branch="False" />
            <line number="41" hits="0" branch="False" />
            <line number="43" hits="0" branch="False" />
            <line number="44" hits="0" branch="False" />
            <line number="52" hits="0" branch="False" />
            <line number="54" hits="0" branch="False" />
            <line number="55" hits="0" branch="False" />
            <line number="56" hits="0" branch="False" />
            <line number="57" hits="0" branch="False" />
            <line number="59" hits="0" branch="False" />
            <line number="60" hits="0" branch="False" />
            <line number="68" hits="0" branch="False" />
            <line number="70" hits="0" branch="False" />
            <line number="73" hits="0" branch="False" />
            <line number="74" hits="0" branch="False" />
            <line number="75" hits="0" branch="False" />
            <line number="76" hits="0" branch="False" />
            <line number="77" hits="0" branch="False" />
            <line number="78" hits="0" branch="False" />
            <line number="79" hits="0" branch="False" />
            <line number="80" hits="0" branch="False" />
            <line number="81" hits="0" branch="False" />
            <line number="82" hits="0" branch="False" />
            <line number="83" hits="0" branch="False" />
            <line number="84" hits="0" branch="False" />
            <line number="85" hits="0" branch="False" />
            <line number="86" hits="0" branch="False" />
            <line number="88" hits="0" branch="False" />
            <line number="89" hits="0" branch="False" />
            <line number="98" hits="0" branch="False" />
            <line number="99" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="28" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="100" hits="0" branch="False" />
            <line number="103" hits="0" branch="False" />
            <line number="106" hits="0" branch="False" />
            <line number="107" hits="0" branch="False" />
            <line number="108" hits="0" branch="False" />
            <line number="109" hits="0" branch="False" />
            <line number="110" hits="0" branch="False" />
            <line number="111" hits="0" branch="False" />
            <line number="112" hits="0" branch="False" />
            <line number="114" hits="0" branch="False" />
            <line number="115" hits="0" branch="False" />
            <line number="124" hits="0" branch="False" />
            <line number="127" hits="0" branch="False" />
            <line number="128" hits="0" branch="False" />
            <line number="129" hits="0" branch="False" />
            <line number="130" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortServicePool" filename="Extensions\ServiceCollectionExtensions.cs" line-rate="0" branch-rate="0" complexity="14">
          <methods>
            <method name="GetStatus" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="290" hits="0" branch="False" />
                <line number="291" hits="0" branch="False" />
                <line number="292" hits="0" branch="False" />
                <line number="293" hits="0" branch="False" />
                <line number="294" hits="0" branch="False" />
                <line number="295" hits="0" branch="False" />
                <line number="296" hits="0" branch="False" />
                <line number="297" hits="0" branch="False" />
                <line number="298" hits="0" branch="False" />
                <line number="299" hits="0" branch="False" />
                <line number="301" hits="0" branch="False" />
              </lines>
            </method>
            <method name="CreateNewService" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="308" hits="0" branch="False" />
                <line number="309" hits="0" branch="False" />
                <line number="310" hits="0" branch="False" />
                <line number="311" hits="0" branch="False" />
                <line number="313" hits="0" branch="False" />
                <line number="314" hits="0" branch="False" />
                <line number="316" hits="0" branch="False" />
                <line number="317" hits="0" branch="False" />
              </lines>
            </method>
            <method name="Dispose" signature="()" line-rate="0" branch-rate="0" complexity="6">
              <lines>
                <line number="323" hits="0" branch="False" />
                <line number="324" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="325" hits="0" branch="False" />
                <line number="327" hits="0" branch="False" />
                <line number="329" hits="0" branch="False" />
                <line number="330" hits="0" branch="False" />
                <line number="332" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="117" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="333" hits="0" branch="False" />
                <line number="334" hits="0" branch="False" />
                <line number="336" hits="0" branch="False" />
                <line number="337" hits="0" branch="False" />
                <line number="338" hits="0" branch="False" />
                <line number="339" hits="0" branch="False" />
                <line number="340" hits="0" branch="False" />
                <line number="341" hits="0" branch="False" />
                <line number="342" hits="0" branch="False" />
                <line number="343" hits="0" branch="False" />
                <line number="345" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="195" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="346" hits="0" branch="False" />
                <line number="348" hits="0" branch="False" />
                <line number="349" hits="0" branch="False" />
                <line number="350" hits="0" branch="False" />
                <line number="351" hits="0" branch="False" />
                <line number="352" hits="0" branch="False" />
                <line number="353" hits="0" branch="False" />
                <line number="354" hits="0" branch="False" />
                <line number="355" hits="0" branch="False" />
                <line number="357" hits="0" branch="False" />
                <line number="358" hits="0" branch="False" />
                <line number="360" hits="0" branch="False" />
                <line number="361" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(Microsoft.Extensions.Logging.ILogger`1&lt;Liam.SerialPort.Extensions.SerialPortServicePool&gt;,Liam.SerialPort.Interfaces.ISerialPortDiscovery,System.Int32,System.IServiceProvider)" line-rate="0" branch-rate="0" complexity="6">
              <lines>
                <line number="165" hits="0" branch="False" />
                <line number="166" hits="0" branch="False" />
                <line number="167" hits="0" branch="False" />
                <line number="178" hits="0" branch="False" />
                <line number="179" hits="0" branch="False" />
                <line number="180" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="44" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="181" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="66" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="182" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="89" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="183" hits="0" branch="False" />
                <line number="185" hits="0" branch="False" />
                <line number="186" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="290" hits="0" branch="False" />
            <line number="291" hits="0" branch="False" />
            <line number="292" hits="0" branch="False" />
            <line number="293" hits="0" branch="False" />
            <line number="294" hits="0" branch="False" />
            <line number="295" hits="0" branch="False" />
            <line number="296" hits="0" branch="False" />
            <line number="297" hits="0" branch="False" />
            <line number="298" hits="0" branch="False" />
            <line number="299" hits="0" branch="False" />
            <line number="301" hits="0" branch="False" />
            <line number="308" hits="0" branch="False" />
            <line number="309" hits="0" branch="False" />
            <line number="310" hits="0" branch="False" />
            <line number="311" hits="0" branch="False" />
            <line number="313" hits="0" branch="False" />
            <line number="314" hits="0" branch="False" />
            <line number="316" hits="0" branch="False" />
            <line number="317" hits="0" branch="False" />
            <line number="323" hits="0" branch="False" />
            <line number="324" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="325" hits="0" branch="False" />
            <line number="327" hits="0" branch="False" />
            <line number="329" hits="0" branch="False" />
            <line number="330" hits="0" branch="False" />
            <line number="332" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="117" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="333" hits="0" branch="False" />
            <line number="334" hits="0" branch="False" />
            <line number="336" hits="0" branch="False" />
            <line number="337" hits="0" branch="False" />
            <line number="338" hits="0" branch="False" />
            <line number="339" hits="0" branch="False" />
            <line number="340" hits="0" branch="False" />
            <line number="341" hits="0" branch="False" />
            <line number="342" hits="0" branch="False" />
            <line number="343" hits="0" branch="False" />
            <line number="345" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="195" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="346" hits="0" branch="False" />
            <line number="348" hits="0" branch="False" />
            <line number="349" hits="0" branch="False" />
            <line number="350" hits="0" branch="False" />
            <line number="351" hits="0" branch="False" />
            <line number="352" hits="0" branch="False" />
            <line number="353" hits="0" branch="False" />
            <line number="354" hits="0" branch="False" />
            <line number="355" hits="0" branch="False" />
            <line number="357" hits="0" branch="False" />
            <line number="358" hits="0" branch="False" />
            <line number="360" hits="0" branch="False" />
            <line number="361" hits="0" branch="False" />
            <line number="165" hits="0" branch="False" />
            <line number="166" hits="0" branch="False" />
            <line number="167" hits="0" branch="False" />
            <line number="178" hits="0" branch="False" />
            <line number="179" hits="0" branch="False" />
            <line number="180" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="44" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="181" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="66" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="182" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="89" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="183" hits="0" branch="False" />
            <line number="185" hits="0" branch="False" />
            <line number="186" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortServicePool/&lt;&gt;c__DisplayClass10_0/&lt;&lt;ReturnServiceAsync&gt;b__0&gt;d" filename="Extensions\ServiceCollectionExtensions.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="261" hits="0" branch="False" />
                <line number="263" hits="0" branch="False" />
                <line number="264" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="42" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="265" hits="0" branch="False" />
                <line number="266" hits="0" branch="False" />
                <line number="267" hits="0" branch="False" />
                <line number="268" hits="0" branch="False" />
                <line number="269" hits="0" branch="False" />
                <line number="270" hits="0" branch="False" />
                <line number="271" hits="0" branch="False" />
                <line number="272" hits="0" branch="False" />
                <line number="273" hits="0" branch="False" />
                <line number="274" hits="0" branch="False" />
                <line number="275" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="261" hits="0" branch="False" />
            <line number="263" hits="0" branch="False" />
            <line number="264" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="42" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="265" hits="0" branch="False" />
            <line number="266" hits="0" branch="False" />
            <line number="267" hits="0" branch="False" />
            <line number="268" hits="0" branch="False" />
            <line number="269" hits="0" branch="False" />
            <line number="270" hits="0" branch="False" />
            <line number="271" hits="0" branch="False" />
            <line number="272" hits="0" branch="False" />
            <line number="273" hits="0" branch="False" />
            <line number="274" hits="0" branch="False" />
            <line number="275" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortServicePool/&lt;GetServiceAsync&gt;d__9" filename="Extensions\ServiceCollectionExtensions.cs" line-rate="0" branch-rate="0" complexity="10">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="10">
              <lines>
                <line number="193" hits="0" branch="False" />
                <line number="194" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="31" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="195" hits="0" branch="False" />
                <line number="197" hits="0" branch="False" />
                <line number="198" hits="0" branch="False" />
                <line number="200" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="108" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="201" hits="0" branch="False" />
                <line number="202" hits="0" branch="False" />
                <line number="203" hits="0" branch="False" />
                <line number="204" hits="0" branch="False" />
                <line number="205" hits="0" branch="False" />
                <line number="209" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="280" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="210" hits="0" branch="False" />
                <line number="211" hits="0" branch="False" />
                <line number="212" hits="0" branch="False" />
                <line number="213" hits="0" branch="False" />
                <line number="214" hits="0" branch="False" />
                <line number="216" hits="0" branch="False" />
                <line number="219" hits="0" branch="False" />
                <line number="221" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="779" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="222" hits="0" branch="False" />
                <line number="223" hits="0" branch="False" />
                <line number="225" hits="0" branch="False" />
                <line number="226" hits="0" branch="False" />
                <line number="227" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="641" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="228" hits="0" branch="False" />
                <line number="229" hits="0" branch="False" />
                <line number="230" hits="0" branch="False" />
                <line number="231" hits="0" branch="False" />
                <line number="232" hits="0" branch="False" />
                <line number="234" hits="0" branch="False" />
                <line number="235" hits="0" branch="False" />
                <line number="237" hits="0" branch="False" />
                <line number="238" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="193" hits="0" branch="False" />
            <line number="194" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="31" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="195" hits="0" branch="False" />
            <line number="197" hits="0" branch="False" />
            <line number="198" hits="0" branch="False" />
            <line number="200" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="108" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="201" hits="0" branch="False" />
            <line number="202" hits="0" branch="False" />
            <line number="203" hits="0" branch="False" />
            <line number="204" hits="0" branch="False" />
            <line number="205" hits="0" branch="False" />
            <line number="209" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="280" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="210" hits="0" branch="False" />
            <line number="211" hits="0" branch="False" />
            <line number="212" hits="0" branch="False" />
            <line number="213" hits="0" branch="False" />
            <line number="214" hits="0" branch="False" />
            <line number="216" hits="0" branch="False" />
            <line number="219" hits="0" branch="False" />
            <line number="221" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="779" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="222" hits="0" branch="False" />
            <line number="223" hits="0" branch="False" />
            <line number="225" hits="0" branch="False" />
            <line number="226" hits="0" branch="False" />
            <line number="227" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="641" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="228" hits="0" branch="False" />
            <line number="229" hits="0" branch="False" />
            <line number="230" hits="0" branch="False" />
            <line number="231" hits="0" branch="False" />
            <line number="232" hits="0" branch="False" />
            <line number="234" hits="0" branch="False" />
            <line number="235" hits="0" branch="False" />
            <line number="237" hits="0" branch="False" />
            <line number="238" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortServicePool/&lt;ReturnServiceAsync&gt;d__10" filename="Extensions\ServiceCollectionExtensions.cs" line-rate="0" branch-rate="0" complexity="6">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="6">
              <lines>
                <line number="245" hits="0" branch="False" />
                <line number="246" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="79" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="247" hits="0" branch="False" />
                <line number="249" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="99" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="250" hits="0" branch="False" />
                <line number="251" hits="0" branch="False" />
                <line number="252" hits="0" branch="False" />
                <line number="255" hits="0" branch="False" />
                <line number="256" hits="0" branch="False" />
                <line number="257" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="196" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="258" hits="0" branch="False" />
                <line number="260" hits="0" branch="False" />
                <line number="262" hits="0" branch="False" />
                <line number="277" hits="0" branch="False" />
                <line number="278" hits="0" branch="False" />
                <line number="279" hits="0" branch="False" />
                <line number="280" hits="0" branch="False" />
                <line number="282" hits="0" branch="False" />
                <line number="283" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="245" hits="0" branch="False" />
            <line number="246" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="79" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="247" hits="0" branch="False" />
            <line number="249" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="99" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="250" hits="0" branch="False" />
            <line number="251" hits="0" branch="False" />
            <line number="252" hits="0" branch="False" />
            <line number="255" hits="0" branch="False" />
            <line number="256" hits="0" branch="False" />
            <line number="257" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="196" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="258" hits="0" branch="False" />
            <line number="260" hits="0" branch="False" />
            <line number="262" hits="0" branch="False" />
            <line number="277" hits="0" branch="False" />
            <line number="278" hits="0" branch="False" />
            <line number="279" hits="0" branch="False" />
            <line number="280" hits="0" branch="False" />
            <line number="282" hits="0" branch="False" />
            <line number="283" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Extensions.SerialPortPoolStatus" filename="Extensions\ServiceCollectionExtensions.cs" line-rate="0" branch-rate="0" complexity="7">
          <methods>
            <method name="get_MaxSize" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="372" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_AvailableCount" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="377" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_UsedCount" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="382" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_TotalCount" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="387" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_UsageRate" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="392" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="7" type="jump" coverage="0%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name="get_IsFull" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="397" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="372" hits="0" branch="False" />
            <line number="377" hits="0" branch="False" />
            <line number="382" hits="0" branch="False" />
            <line number="387" hits="0" branch="False" />
            <line number="392" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="7" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="397" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Exceptions.SerialPortException" filename="Exceptions\SerialPortException.cs" line-rate="0" branch-rate="1" complexity="9">
          <methods>
            <method name="get_PortName" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="11" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_ErrorCode" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="16" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="21" hits="0" branch="False" />
                <line number="22" hits="0" branch="False" />
                <line number="23" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="29" hits="0" branch="False" />
                <line number="30" hits="0" branch="False" />
                <line number="31" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Exception)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="38" hits="0" branch="False" />
                <line number="39" hits="0" branch="False" />
                <line number="40" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="47" hits="0" branch="False" />
                <line number="48" hits="0" branch="False" />
                <line number="49" hits="0" branch="False" />
                <line number="50" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String,System.Exception)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="58" hits="0" branch="False" />
                <line number="59" hits="0" branch="False" />
                <line number="60" hits="0" branch="False" />
                <line number="61" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="69" hits="0" branch="False" />
                <line number="70" hits="0" branch="False" />
                <line number="71" hits="0" branch="False" />
                <line number="72" hits="0" branch="False" />
                <line number="73" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String,System.String,System.Exception)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="82" hits="0" branch="False" />
                <line number="83" hits="0" branch="False" />
                <line number="84" hits="0" branch="False" />
                <line number="85" hits="0" branch="False" />
                <line number="86" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="11" hits="0" branch="False" />
            <line number="16" hits="0" branch="False" />
            <line number="21" hits="0" branch="False" />
            <line number="22" hits="0" branch="False" />
            <line number="23" hits="0" branch="False" />
            <line number="29" hits="0" branch="False" />
            <line number="30" hits="0" branch="False" />
            <line number="31" hits="0" branch="False" />
            <line number="38" hits="0" branch="False" />
            <line number="39" hits="0" branch="False" />
            <line number="40" hits="0" branch="False" />
            <line number="47" hits="0" branch="False" />
            <line number="48" hits="0" branch="False" />
            <line number="49" hits="0" branch="False" />
            <line number="50" hits="0" branch="False" />
            <line number="58" hits="0" branch="False" />
            <line number="59" hits="0" branch="False" />
            <line number="60" hits="0" branch="False" />
            <line number="61" hits="0" branch="False" />
            <line number="69" hits="0" branch="False" />
            <line number="70" hits="0" branch="False" />
            <line number="71" hits="0" branch="False" />
            <line number="72" hits="0" branch="False" />
            <line number="73" hits="0" branch="False" />
            <line number="82" hits="0" branch="False" />
            <line number="83" hits="0" branch="False" />
            <line number="84" hits="0" branch="False" />
            <line number="85" hits="0" branch="False" />
            <line number="86" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Exceptions.SerialPortConnectionException" filename="Exceptions\SerialPortException.cs" line-rate="0" branch-rate="1" complexity="5">
          <methods>
            <method name=".ctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="97" hits="0" branch="False" />
                <line number="98" hits="0" branch="False" />
                <line number="99" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="105" hits="0" branch="False" />
                <line number="106" hits="0" branch="False" />
                <line number="107" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Exception)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="114" hits="0" branch="False" />
                <line number="115" hits="0" branch="False" />
                <line number="116" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="123" hits="0" branch="False" />
                <line number="124" hits="0" branch="False" />
                <line number="125" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String,System.Exception)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="133" hits="0" branch="False" />
                <line number="134" hits="0" branch="False" />
                <line number="135" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="97" hits="0" branch="False" />
            <line number="98" hits="0" branch="False" />
            <line number="99" hits="0" branch="False" />
            <line number="105" hits="0" branch="False" />
            <line number="106" hits="0" branch="False" />
            <line number="107" hits="0" branch="False" />
            <line number="114" hits="0" branch="False" />
            <line number="115" hits="0" branch="False" />
            <line number="116" hits="0" branch="False" />
            <line number="123" hits="0" branch="False" />
            <line number="124" hits="0" branch="False" />
            <line number="125" hits="0" branch="False" />
            <line number="133" hits="0" branch="False" />
            <line number="134" hits="0" branch="False" />
            <line number="135" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Exceptions.SerialPortTimeoutException" filename="Exceptions\SerialPortException.cs" line-rate="0" branch-rate="1" complexity="5">
          <methods>
            <method name="get_Timeout" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="146" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_OperationType" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="151" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.TimeSpan,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="159" hits="0" branch="False" />
                <line number="160" hits="0" branch="False" />
                <line number="161" hits="0" branch="False" />
                <line number="162" hits="0" branch="False" />
                <line number="163" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.TimeSpan,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="172" hits="0" branch="False" />
                <line number="173" hits="0" branch="False" />
                <line number="174" hits="0" branch="False" />
                <line number="175" hits="0" branch="False" />
                <line number="176" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.TimeSpan,System.String,System.Exception)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="186" hits="0" branch="False" />
                <line number="187" hits="0" branch="False" />
                <line number="188" hits="0" branch="False" />
                <line number="189" hits="0" branch="False" />
                <line number="190" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="146" hits="0" branch="False" />
            <line number="151" hits="0" branch="False" />
            <line number="159" hits="0" branch="False" />
            <line number="160" hits="0" branch="False" />
            <line number="161" hits="0" branch="False" />
            <line number="162" hits="0" branch="False" />
            <line number="163" hits="0" branch="False" />
            <line number="172" hits="0" branch="False" />
            <line number="173" hits="0" branch="False" />
            <line number="174" hits="0" branch="False" />
            <line number="175" hits="0" branch="False" />
            <line number="176" hits="0" branch="False" />
            <line number="186" hits="0" branch="False" />
            <line number="187" hits="0" branch="False" />
            <line number="188" hits="0" branch="False" />
            <line number="189" hits="0" branch="False" />
            <line number="190" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Exceptions.SerialPortConfigurationException" filename="Exceptions\SerialPortException.cs" line-rate="0" branch-rate="1" complexity="5">
          <methods>
            <method name="get_ParameterName" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="201" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_ParameterValue" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="206" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="212" hits="0" branch="False" />
                <line number="213" hits="0" branch="False" />
                <line number="214" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Object,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="222" hits="0" branch="False" />
                <line number="223" hits="0" branch="False" />
                <line number="224" hits="0" branch="False" />
                <line number="225" hits="0" branch="False" />
                <line number="226" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String,System.Object,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="235" hits="0" branch="False" />
                <line number="236" hits="0" branch="False" />
                <line number="237" hits="0" branch="False" />
                <line number="238" hits="0" branch="False" />
                <line number="239" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="201" hits="0" branch="False" />
            <line number="206" hits="0" branch="False" />
            <line number="212" hits="0" branch="False" />
            <line number="213" hits="0" branch="False" />
            <line number="214" hits="0" branch="False" />
            <line number="222" hits="0" branch="False" />
            <line number="223" hits="0" branch="False" />
            <line number="224" hits="0" branch="False" />
            <line number="225" hits="0" branch="False" />
            <line number="226" hits="0" branch="False" />
            <line number="235" hits="0" branch="False" />
            <line number="236" hits="0" branch="False" />
            <line number="237" hits="0" branch="False" />
            <line number="238" hits="0" branch="False" />
            <line number="239" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Exceptions.SerialPortDataException" filename="Exceptions\SerialPortException.cs" line-rate="0" branch-rate="1" complexity="4">
          <methods>
            <method name="get_DataLength" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="250" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Int32)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="257" hits="0" branch="False" />
                <line number="258" hits="0" branch="False" />
                <line number="259" hits="0" branch="False" />
                <line number="260" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String,System.Int32)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="268" hits="0" branch="False" />
                <line number="269" hits="0" branch="False" />
                <line number="270" hits="0" branch="False" />
                <line number="271" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String,System.Int32,System.Exception)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="280" hits="0" branch="False" />
                <line number="281" hits="0" branch="False" />
                <line number="282" hits="0" branch="False" />
                <line number="283" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="250" hits="0" branch="False" />
            <line number="257" hits="0" branch="False" />
            <line number="258" hits="0" branch="False" />
            <line number="259" hits="0" branch="False" />
            <line number="260" hits="0" branch="False" />
            <line number="268" hits="0" branch="False" />
            <line number="269" hits="0" branch="False" />
            <line number="270" hits="0" branch="False" />
            <line number="271" hits="0" branch="False" />
            <line number="280" hits="0" branch="False" />
            <line number="281" hits="0" branch="False" />
            <line number="282" hits="0" branch="False" />
            <line number="283" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Events.SerialPortEventArgs" filename="Events\SerialPortEventArgs.cs" line-rate="0" branch-rate="0" complexity="4">
          <methods>
            <method name="get_Timestamp" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="13" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_PortName" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="18" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="24" hits="0" branch="False" />
                <line number="25" hits="0" branch="False" />
                <line number="26" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="22" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="27" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="13" hits="0" branch="False" />
            <line number="18" hits="0" branch="False" />
            <line number="24" hits="0" branch="False" />
            <line number="25" hits="0" branch="False" />
            <line number="26" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="22" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="27" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Events.ConnectionStatusChangedEventArgs" filename="Events\SerialPortEventArgs.cs" line-rate="0" branch-rate="1" complexity="4">
          <methods>
            <method name="get_OldStatus" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="38" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_NewStatus" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="43" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Reason" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="48" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,Liam.SerialPort.Models.ConnectionStatus,Liam.SerialPort.Models.ConnectionStatus,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="58" hits="0" branch="False" />
                <line number="59" hits="0" branch="False" />
                <line number="60" hits="0" branch="False" />
                <line number="61" hits="0" branch="False" />
                <line number="62" hits="0" branch="False" />
                <line number="63" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="38" hits="0" branch="False" />
            <line number="43" hits="0" branch="False" />
            <line number="48" hits="0" branch="False" />
            <line number="58" hits="0" branch="False" />
            <line number="59" hits="0" branch="False" />
            <line number="60" hits="0" branch="False" />
            <line number="61" hits="0" branch="False" />
            <line number="62" hits="0" branch="False" />
            <line number="63" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Events.DataReceivedEventArgs" filename="Events\SerialPortEventArgs.cs" line-rate="0" branch-rate="0" complexity="7">
          <methods>
            <method name="get_Data" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="74" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Length" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="79" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_DataAsString" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="84" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_DataAsHex" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="89" hits="0" branch="False" />
              </lines>
            </method>
            <method name="GetDataAsString" signature="(System.Text.Encoding)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="108" hits="0" branch="False" />
                <line number="109" hits="0" branch="False" />
                <line number="110" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Byte[])" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="97" hits="0" branch="False" />
                <line number="98" hits="0" branch="False" />
                <line number="99" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="12" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="100" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="74" hits="0" branch="False" />
            <line number="79" hits="0" branch="False" />
            <line number="84" hits="0" branch="False" />
            <line number="89" hits="0" branch="False" />
            <line number="108" hits="0" branch="False" />
            <line number="109" hits="0" branch="False" />
            <line number="110" hits="0" branch="False" />
            <line number="97" hits="0" branch="False" />
            <line number="98" hits="0" branch="False" />
            <line number="99" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="12" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="100" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Events.DataSentEventArgs" filename="Events\SerialPortEventArgs.cs" line-rate="0" branch-rate="0" complexity="7">
          <methods>
            <method name="get_Data" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="121" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Length" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="126" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Success" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="131" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Duration" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="136" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Error" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="141" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Byte[],System.Boolean,System.TimeSpan,System.String)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="152" hits="0" branch="False" />
                <line number="153" hits="0" branch="False" />
                <line number="154" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="12" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="155" hits="0" branch="False" />
                <line number="156" hits="0" branch="False" />
                <line number="157" hits="0" branch="False" />
                <line number="158" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="121" hits="0" branch="False" />
            <line number="126" hits="0" branch="False" />
            <line number="131" hits="0" branch="False" />
            <line number="136" hits="0" branch="False" />
            <line number="141" hits="0" branch="False" />
            <line number="152" hits="0" branch="False" />
            <line number="153" hits="0" branch="False" />
            <line number="154" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="12" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="155" hits="0" branch="False" />
            <line number="156" hits="0" branch="False" />
            <line number="157" hits="0" branch="False" />
            <line number="158" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Events.SerialPortErrorEventArgs" filename="Events\SerialPortEventArgs.cs" line-rate="0" branch-rate="0" complexity="6">
          <methods>
            <method name="get_ErrorType" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="169" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Message" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="174" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Exception" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="179" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_IsFatal" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="184" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,Liam.SerialPort.Events.SerialPortErrorType,System.String,System.Exception,System.Boolean)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="195" hits="0" branch="False" />
                <line number="196" hits="0" branch="False" />
                <line number="197" hits="0" branch="False" />
                <line number="198" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="19" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="199" hits="0" branch="False" />
                <line number="200" hits="0" branch="False" />
                <line number="201" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="169" hits="0" branch="False" />
            <line number="174" hits="0" branch="False" />
            <line number="179" hits="0" branch="False" />
            <line number="184" hits="0" branch="False" />
            <line number="195" hits="0" branch="False" />
            <line number="196" hits="0" branch="False" />
            <line number="197" hits="0" branch="False" />
            <line number="198" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="19" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="199" hits="0" branch="False" />
            <line number="200" hits="0" branch="False" />
            <line number="201" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Events.DeviceChangedEventArgs" filename="Events\SerialPortEventArgs.cs" line-rate="0" branch-rate="0" complexity="5">
          <methods>
            <method name="get_ChangeType" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="212" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_DeviceInfo" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="217" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Timestamp" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="222" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(Liam.SerialPort.Events.DeviceChangeType,Liam.SerialPort.Models.SerialPortInfo)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="229" hits="0" branch="False" />
                <line number="230" hits="0" branch="False" />
                <line number="231" hits="0" branch="False" />
                <line number="232" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="29" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="233" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="212" hits="0" branch="False" />
            <line number="217" hits="0" branch="False" />
            <line number="222" hits="0" branch="False" />
            <line number="229" hits="0" branch="False" />
            <line number="230" hits="0" branch="False" />
            <line number="231" hits="0" branch="False" />
            <line number="232" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="29" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="233" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Constants.SerialPortConstants/BaudRates" filename="Constants\SerialPortConstants.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name=".cctor" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="103" hits="1" branch="False" />
                <line number="104" hits="1" branch="False" />
                <line number="105" hits="1" branch="False" />
                <line number="106" hits="1" branch="False" />
                <line number="107" hits="1" branch="False" />
                <line number="112" hits="1" branch="False" />
                <line number="113" hits="1" branch="False" />
                <line number="114" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="103" hits="1" branch="False" />
            <line number="104" hits="1" branch="False" />
            <line number="105" hits="1" branch="False" />
            <line number="106" hits="1" branch="False" />
            <line number="107" hits="1" branch="False" />
            <line number="112" hits="1" branch="False" />
            <line number="113" hits="1" branch="False" />
            <line number="114" hits="1" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Constants.SerialPortConstants/Platform" filename="Constants\SerialPortConstants.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name=".cctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="164" hits="0" branch="False" />
                <line number="165" hits="0" branch="False" />
                <line number="166" hits="0" branch="False" />
                <line number="167" hits="0" branch="False" />
                <line number="168" hits="0" branch="False" />
                <line number="169" hits="0" branch="False" />
                <line number="174" hits="0" branch="False" />
                <line number="175" hits="0" branch="False" />
                <line number="176" hits="0" branch="False" />
                <line number="177" hits="0" branch="False" />
                <line number="178" hits="0" branch="False" />
                <line number="179" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="164" hits="0" branch="False" />
            <line number="165" hits="0" branch="False" />
            <line number="166" hits="0" branch="False" />
            <line number="167" hits="0" branch="False" />
            <line number="168" hits="0" branch="False" />
            <line number="169" hits="0" branch="False" />
            <line number="174" hits="0" branch="False" />
            <line number="175" hits="0" branch="False" />
            <line number="176" hits="0" branch="False" />
            <line number="177" hits="0" branch="False" />
            <line number="178" hits="0" branch="False" />
            <line number="179" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Constants.SerialPortConstants/DataFormats" filename="Constants\SerialPortConstants.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name=".cctor" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="205" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="205" hits="1" branch="False" />
          </lines>
        </class>
        <class name="Liam.SerialPort.Constants.SerialPortConstants/DataFormats/Terminators" filename="Constants\SerialPortConstants.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name=".cctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="222" hits="0" branch="False" />
                <line number="223" hits="0" branch="False" />
                <line number="224" hits="0" branch="False" />
                <line number="225" hits="0" branch="False" />
                <line number="226" hits="0" branch="False" />
                <line number="227" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="222" hits="0" branch="False" />
            <line number="223" hits="0" branch="False" />
            <line number="224" hits="0" branch="False" />
            <line number="225" hits="0" branch="False" />
            <line number="226" hits="0" branch="False" />
            <line number="227" hits="0" branch="False" />
          </lines>
        </class>
      </classes>
    </package>
  </packages>
</coverage>