<?xml version="1.0"?>
<doc>
    <assembly>
        <name><PERSON>.Logging</name>
    </assembly>
    <members>
        <member name="T:<PERSON>.Logging.Constants.LogLevel">
            <summary>
            定义日志级别枚举
            </summary>
        </member>
        <member name="F:<PERSON>.Logging.Constants.LogLevel.Trace">
            <summary>
            跟踪级别 - 最详细的日志信息，通常只在开发时使用
            </summary>
        </member>
        <member name="F:<PERSON>.Logging.Constants.LogLevel.Debug">
            <summary>
            调试级别 - 调试信息，通常只在开发和测试时使用
            </summary>
        </member>
        <member name="F:<PERSON>.Logging.Constants.LogLevel.Information">
            <summary>
            信息级别 - 一般信息，记录程序的正常运行流程
            </summary>
        </member>
        <member name="F:Liam.Logging.Constants.LogLevel.Warning">
            <summary>
            警告级别 - 警告信息，表示可能存在问题但不影响程序运行
            </summary>
        </member>
        <member name="F:<PERSON>.Logging.Constants.LogLevel.Error">
            <summary>
            错误级别 - 错误信息，表示发生了错误但程序可以继续运行
            </summary>
        </member>
        <member name="F:Liam.Logging.Constants.LogLevel.Critical">
            <summary>
            严重错误级别 - 严重错误，可能导致程序崩溃
            </summary>
        </member>
        <member name="F:Liam.Logging.Constants.LogLevel.None">
            <summary>
            无日志 - 禁用所有日志记录
            </summary>
        </member>
        <member name="T:Liam.Logging.Constants.LogLevelConstants">
            <summary>
            日志级别相关的常量和工具方法
            </summary>
        </member>
        <member name="F:Liam.Logging.Constants.LogLevelConstants.LevelNames">
            <summary>
            日志级别名称映射
            </summary>
        </member>
        <member name="F:Liam.Logging.Constants.LogLevelConstants.LevelColors">
            <summary>
            日志级别颜色映射（用于控制台输出）
            </summary>
        </member>
        <member name="M:Liam.Logging.Constants.LogLevelConstants.GetLevelName(Liam.Logging.Constants.LogLevel)">
            <summary>
            获取日志级别的显示名称
            </summary>
            <param name="level">日志级别</param>
            <returns>显示名称</returns>
        </member>
        <member name="M:Liam.Logging.Constants.LogLevelConstants.GetLevelColor(Liam.Logging.Constants.LogLevel)">
            <summary>
            获取日志级别的控制台颜色
            </summary>
            <param name="level">日志级别</param>
            <returns>控制台颜色</returns>
        </member>
        <member name="M:Liam.Logging.Constants.LogLevelConstants.IsEnabled(Liam.Logging.Constants.LogLevel,Liam.Logging.Constants.LogLevel)">
            <summary>
            判断指定级别是否应该记录日志
            </summary>
            <param name="currentLevel">当前日志级别</param>
            <param name="minimumLevel">最小日志级别</param>
            <returns>是否应该记录</returns>
        </member>
        <member name="T:Liam.Logging.Exceptions.LoggingException">
            <summary>
            日志记录基础异常类
            </summary>
        </member>
        <member name="M:Liam.Logging.Exceptions.LoggingException.#ctor">
            <summary>
            初始化日志记录异常
            </summary>
        </member>
        <member name="M:Liam.Logging.Exceptions.LoggingException.#ctor(System.String)">
            <summary>
            初始化日志记录异常
            </summary>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Logging.Exceptions.LoggingException.#ctor(System.String,System.Exception)">
            <summary>
            初始化日志记录异常
            </summary>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.Logging.Exceptions.LogProviderException">
            <summary>
            日志提供程序异常类
            </summary>
        </member>
        <member name="P:Liam.Logging.Exceptions.LogProviderException.ProviderName">
            <summary>
            提供程序名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogProviderException.#ctor(System.String)">
            <summary>
            初始化日志提供程序异常
            </summary>
            <param name="providerName">提供程序名称</param>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogProviderException.#ctor(System.String,System.String)">
            <summary>
            初始化日志提供程序异常
            </summary>
            <param name="providerName">提供程序名称</param>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogProviderException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            初始化日志提供程序异常
            </summary>
            <param name="providerName">提供程序名称</param>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.Logging.Exceptions.LogConfigurationException">
            <summary>
            日志配置异常类
            </summary>
        </member>
        <member name="P:Liam.Logging.Exceptions.LogConfigurationException.ConfigurationSection">
            <summary>
            配置节点名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogConfigurationException.#ctor">
            <summary>
            初始化日志配置异常
            </summary>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogConfigurationException.#ctor(System.String)">
            <summary>
            初始化日志配置异常
            </summary>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogConfigurationException.#ctor(System.String,System.String)">
            <summary>
            初始化日志配置异常
            </summary>
            <param name="configurationSection">配置节点名称</param>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogConfigurationException.#ctor(System.String,System.Exception)">
            <summary>
            初始化日志配置异常
            </summary>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.Logging.Exceptions.LogFormattingException">
            <summary>
            日志格式化异常类
            </summary>
        </member>
        <member name="P:Liam.Logging.Exceptions.LogFormattingException.FormatterName">
            <summary>
            格式化器名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogFormattingException.#ctor(System.String)">
            <summary>
            初始化日志格式化异常
            </summary>
            <param name="formatterName">格式化器名称</param>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogFormattingException.#ctor(System.String,System.String)">
            <summary>
            初始化日志格式化异常
            </summary>
            <param name="formatterName">格式化器名称</param>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogFormattingException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            初始化日志格式化异常
            </summary>
            <param name="formatterName">格式化器名称</param>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.Logging.Exceptions.LogFileException">
            <summary>
            日志文件异常类
            </summary>
        </member>
        <member name="P:Liam.Logging.Exceptions.LogFileException.FilePath">
            <summary>
            文件路径
            </summary>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogFileException.#ctor(System.String)">
            <summary>
            初始化日志文件异常
            </summary>
            <param name="filePath">文件路径</param>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogFileException.#ctor(System.String,System.String)">
            <summary>
            初始化日志文件异常
            </summary>
            <param name="filePath">文件路径</param>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogFileException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            初始化日志文件异常
            </summary>
            <param name="filePath">文件路径</param>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.Logging.Exceptions.LogQueueException">
            <summary>
            日志队列异常类
            </summary>
        </member>
        <member name="P:Liam.Logging.Exceptions.LogQueueException.QueueName">
            <summary>
            队列名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogQueueException.#ctor(System.String)">
            <summary>
            初始化日志队列异常
            </summary>
            <param name="queueName">队列名称</param>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogQueueException.#ctor(System.String,System.String)">
            <summary>
            初始化日志队列异常
            </summary>
            <param name="queueName">队列名称</param>
            <param name="message">错误消息</param>
        </member>
        <member name="M:Liam.Logging.Exceptions.LogQueueException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            初始化日志队列异常
            </summary>
            <param name="queueName">队列名称</param>
            <param name="message">错误消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Liam.Logging.Extensions.LoggerExtensions">
            <summary>
            日志记录器扩展方法
            </summary>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogTraceStructured(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Object[])">
            <summary>
            记录结构化跟踪日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="messageTemplate">消息模板</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogDebugStructured(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Object[])">
            <summary>
            记录结构化调试日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="messageTemplate">消息模板</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogInformationStructured(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Object[])">
            <summary>
            记录结构化信息日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="messageTemplate">消息模板</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogWarningStructured(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Object[])">
            <summary>
            记录结构化警告日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="messageTemplate">消息模板</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogErrorStructured(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Object[])">
            <summary>
            记录结构化错误日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="messageTemplate">消息模板</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogCriticalStructured(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Object[])">
            <summary>
            记录结构化严重错误日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="messageTemplate">消息模板</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogTraceAsync(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Threading.CancellationToken,System.String,System.String,System.Int32)">
            <summary>
            异步记录跟踪日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="message">日志消息</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogDebugAsync(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Threading.CancellationToken,System.String,System.String,System.Int32)">
            <summary>
            异步记录调试日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="message">日志消息</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogInformationAsync(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Threading.CancellationToken,System.String,System.String,System.Int32)">
            <summary>
            异步记录信息日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="message">日志消息</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogWarningAsync(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Exception,System.Threading.CancellationToken,System.String,System.String,System.Int32)">
            <summary>
            异步记录警告日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogErrorAsync(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Exception,System.Threading.CancellationToken,System.String,System.String,System.Int32)">
            <summary>
            异步记录错误日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogCriticalAsync(Liam.Logging.Interfaces.ILiamLogger,System.String,System.Exception,System.Threading.CancellationToken,System.String,System.String,System.Int32)">
            <summary>
            异步记录严重错误日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogMethodEntry(Liam.Logging.Interfaces.ILiamLogger,System.Object,System.String,System.String,System.Int32)">
            <summary>
            记录方法进入日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="parameters">方法参数</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogMethodExit(Liam.Logging.Interfaces.ILiamLogger,System.Object,System.String,System.String,System.Int32)">
            <summary>
            记录方法退出日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="result">返回值</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.LogPerformance(Liam.Logging.Interfaces.ILiamLogger,System.String,System.TimeSpan,System.String,System.String,System.Int32)">
            <summary>
            记录性能日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="operationName">操作名称</param>
            <param name="duration">耗时</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.WithScope``1(Liam.Logging.Interfaces.ILiamLogger,``0,System.Action)">
            <summary>
            使用作用域记录日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="scopeState">作用域状态</param>
            <param name="action">要执行的操作</param>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.WithScopeAsync``1(Liam.Logging.Interfaces.ILiamLogger,``0,System.Func{System.Threading.Tasks.Task})">
            <summary>
            异步使用作用域记录日志
            </summary>
            <param name="logger">日志记录器</param>
            <param name="scopeState">作用域状态</param>
            <param name="asyncAction">要执行的异步操作</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.WithScope``2(Liam.Logging.Interfaces.ILiamLogger,``0,System.Func{``1})">
            <summary>
            使用作用域记录日志（带返回值）
            </summary>
            <typeparam name="TState">作用域状态类型</typeparam>
            <typeparam name="TResult">返回值类型</typeparam>
            <param name="logger">日志记录器</param>
            <param name="scopeState">作用域状态</param>
            <param name="func">要执行的函数</param>
            <returns>函数返回值</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggerExtensions.WithScopeAsync``2(Liam.Logging.Interfaces.ILiamLogger,``0,System.Func{System.Threading.Tasks.Task{``1}})">
            <summary>
            异步使用作用域记录日志（带返回值）
            </summary>
            <typeparam name="TState">作用域状态类型</typeparam>
            <typeparam name="TResult">返回值类型</typeparam>
            <param name="logger">日志记录器</param>
            <param name="scopeState">作用域状态</param>
            <param name="asyncFunc">要执行的异步函数</param>
            <returns>异步任务</returns>
        </member>
        <member name="T:Liam.Logging.Extensions.ServiceCollectionExtensions">
            <summary>
            服务集合扩展方法
            </summary>
        </member>
        <member name="M:Liam.Logging.Extensions.ServiceCollectionExtensions.AddLiamLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection,Liam.Logging.Models.LogConfiguration)">
            <summary>
            添加Liam日志记录服务
            </summary>
            <param name="services">服务集合</param>
            <param name="configuration">配置</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.ServiceCollectionExtensions.AddLiamLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration,System.String)">
            <summary>
            添加Liam日志记录服务（从配置文件）
            </summary>
            <param name="services">服务集合</param>
            <param name="configuration">配置根</param>
            <param name="sectionName">配置节名称</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.ServiceCollectionExtensions.AddConsoleLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Liam.Logging.Models.ConsoleLogConfiguration})">
            <summary>
            添加控制台日志提供程序
            </summary>
            <param name="services">服务集合</param>
            <param name="configure">配置委托</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.ServiceCollectionExtensions.AddFileLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Liam.Logging.Models.FileLogConfiguration})">
            <summary>
            添加文件日志提供程序
            </summary>
            <param name="services">服务集合</param>
            <param name="configure">配置委托</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.ServiceCollectionExtensions.ConfigureLiamLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Liam.Logging.Extensions.LoggingBuilder})">
            <summary>
            配置日志记录
            </summary>
            <param name="services">服务集合</param>
            <param name="configure">配置委托</param>
            <returns>服务集合</returns>
        </member>
        <member name="T:Liam.Logging.Extensions.LoggingBuilder">
            <summary>
            日志记录构建器
            </summary>
        </member>
        <member name="P:Liam.Logging.Extensions.LoggingBuilder.Services">
            <summary>
            服务集合
            </summary>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.#ctor(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            初始化日志记录构建器
            </summary>
            <param name="services">服务集合</param>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.AddConsole(System.Action{Liam.Logging.Models.ConsoleLogConfiguration})">
            <summary>
            添加控制台日志
            </summary>
            <param name="configure">配置委托</param>
            <returns>构建器</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.AddFile(System.Action{Liam.Logging.Models.FileLogConfiguration})">
            <summary>
            添加文件日志
            </summary>
            <param name="configure">配置委托</param>
            <returns>构建器</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.AddProvider``1">
            <summary>
            添加自定义提供程序
            </summary>
            <typeparam name="T">提供程序类型</typeparam>
            <returns>构建器</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.AddProvider(Liam.Logging.Interfaces.ILogProvider)">
            <summary>
            添加自定义提供程序
            </summary>
            <param name="provider">提供程序实例</param>
            <returns>构建器</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.AddFilter``1">
            <summary>
            添加自定义过滤器
            </summary>
            <typeparam name="T">过滤器类型</typeparam>
            <returns>构建器</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.AddFilter(Liam.Logging.Interfaces.ILogFilter)">
            <summary>
            添加自定义过滤器
            </summary>
            <param name="filter">过滤器实例</param>
            <returns>构建器</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.SetMinimumLevel(Liam.Logging.Constants.LogLevel)">
            <summary>
            设置最小日志级别
            </summary>
            <param name="level">日志级别</param>
            <returns>构建器</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.EnableAsync(System.Int32,System.Int32,System.Int32)">
            <summary>
            启用异步日志记录
            </summary>
            <param name="queueSize">队列大小</param>
            <param name="batchSize">批处理大小</param>
            <param name="batchTimeout">批处理超时时间</param>
            <returns>构建器</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.EnablePerformanceMonitoring">
            <summary>
            启用性能监控
            </summary>
            <returns>构建器</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.DisablePerformanceMonitoring">
            <summary>
            禁用性能监控
            </summary>
            <returns>构建器</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.EnableScopes">
            <summary>
            启用作用域
            </summary>
            <returns>构建器</returns>
        </member>
        <member name="M:Liam.Logging.Extensions.LoggingBuilder.DisableScopes">
            <summary>
            禁用作用域
            </summary>
            <returns>构建器</returns>
        </member>
        <member name="T:Liam.Logging.Filters.LogLevelFilter">
            <summary>
            日志级别过滤器
            </summary>
        </member>
        <member name="P:Liam.Logging.Filters.LogLevelFilter.Name">
            <summary>
            过滤器名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Filters.LogLevelFilter.#ctor(Liam.Logging.Constants.LogLevel,Liam.Logging.Constants.LogLevel)">
            <summary>
            初始化日志级别过滤器
            </summary>
            <param name="minimumLevel">最小日志级别</param>
            <param name="maximumLevel">最大日志级别</param>
        </member>
        <member name="M:Liam.Logging.Filters.LogLevelFilter.ShouldLog(Liam.Logging.Models.LogEvent)">
            <summary>
            判断是否应该记录日志
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>是否应该记录</returns>
        </member>
        <member name="T:Liam.Logging.Filters.LogCategoryFilter">
            <summary>
            日志类别过滤器
            </summary>
        </member>
        <member name="P:Liam.Logging.Filters.LogCategoryFilter.Name">
            <summary>
            过滤器名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Filters.LogCategoryFilter.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            初始化日志类别过滤器（白名单模式）
            </summary>
            <param name="allowedCategories">允许的类别</param>
        </member>
        <member name="M:Liam.Logging.Filters.LogCategoryFilter.#ctor(System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <summary>
            初始化日志类别过滤器（黑名单模式）
            </summary>
            <param name="blockedCategories">阻止的类别</param>
            <param name="isBlacklist">是否为黑名单模式</param>
        </member>
        <member name="M:Liam.Logging.Filters.LogCategoryFilter.ShouldLog(Liam.Logging.Models.LogEvent)">
            <summary>
            判断是否应该记录日志
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>是否应该记录</returns>
        </member>
        <member name="T:Liam.Logging.Filters.LogMessageFilter">
            <summary>
            日志消息内容过滤器
            </summary>
        </member>
        <member name="P:Liam.Logging.Filters.LogMessageFilter.Name">
            <summary>
            过滤器名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Filters.LogMessageFilter.#ctor(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            初始化日志消息内容过滤器
            </summary>
            <param name="includePatterns">包含的正则表达式模式</param>
            <param name="excludePatterns">排除的正则表达式模式</param>
        </member>
        <member name="M:Liam.Logging.Filters.LogMessageFilter.ShouldLog(Liam.Logging.Models.LogEvent)">
            <summary>
            判断是否应该记录日志
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>是否应该记录</returns>
        </member>
        <member name="T:Liam.Logging.Filters.LogPropertyFilter">
            <summary>
            日志属性过滤器
            </summary>
        </member>
        <member name="P:Liam.Logging.Filters.LogPropertyFilter.Name">
            <summary>
            过滤器名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Filters.LogPropertyFilter.#ctor(System.Collections.Generic.Dictionary{System.String,System.Object},System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            初始化日志属性过滤器
            </summary>
            <param name="requiredProperties">必需的属性</param>
            <param name="excludedProperties">排除的属性</param>
        </member>
        <member name="M:Liam.Logging.Filters.LogPropertyFilter.ShouldLog(Liam.Logging.Models.LogEvent)">
            <summary>
            判断是否应该记录日志
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>是否应该记录</returns>
        </member>
        <member name="T:Liam.Logging.Filters.LogTimeRangeFilter">
            <summary>
            时间范围过滤器
            </summary>
        </member>
        <member name="P:Liam.Logging.Filters.LogTimeRangeFilter.Name">
            <summary>
            过滤器名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Filters.LogTimeRangeFilter.#ctor(System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.DayOfWeek[])">
            <summary>
            初始化时间范围过滤器
            </summary>
            <param name="startTime">开始时间</param>
            <param name="endTime">结束时间</param>
            <param name="allowedDays">允许的星期</param>
        </member>
        <member name="M:Liam.Logging.Filters.LogTimeRangeFilter.ShouldLog(Liam.Logging.Models.LogEvent)">
            <summary>
            判断是否应该记录日志
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>是否应该记录</returns>
        </member>
        <member name="T:Liam.Logging.Filters.CompositeLogFilter">
            <summary>
            复合日志过滤器
            </summary>
        </member>
        <member name="P:Liam.Logging.Filters.CompositeLogFilter.Name">
            <summary>
            过滤器名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Filters.CompositeLogFilter.#ctor(System.Collections.Generic.IEnumerable{Liam.Logging.Interfaces.ILogFilter},Liam.Logging.Filters.LogFilterCombineMode)">
            <summary>
            初始化复合日志过滤器
            </summary>
            <param name="filters">子过滤器集合</param>
            <param name="combineMode">组合模式</param>
        </member>
        <member name="M:Liam.Logging.Filters.CompositeLogFilter.ShouldLog(Liam.Logging.Models.LogEvent)">
            <summary>
            判断是否应该记录日志
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>是否应该记录</returns>
        </member>
        <member name="T:Liam.Logging.Filters.LogFilterCombineMode">
            <summary>
            日志过滤器组合模式
            </summary>
        </member>
        <member name="F:Liam.Logging.Filters.LogFilterCombineMode.And">
            <summary>
            与操作（所有过滤器都通过）
            </summary>
        </member>
        <member name="F:Liam.Logging.Filters.LogFilterCombineMode.Or">
            <summary>
            或操作（任一过滤器通过）
            </summary>
        </member>
        <member name="T:Liam.Logging.Formatters.TextLogFormatter">
            <summary>
            文本日志格式化器
            </summary>
        </member>
        <member name="P:Liam.Logging.Formatters.TextLogFormatter.Name">
            <summary>
            格式化器名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Formatters.TextLogFormatter.#ctor(System.String,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            初始化文本日志格式化器
            </summary>
            <param name="timestampFormat">时间戳格式</param>
            <param name="includeScopes">是否包含作用域信息</param>
            <param name="includeExceptionDetails">是否包含异常详细信息</param>
            <param name="includeSourceInfo">是否包含源代码信息</param>
        </member>
        <member name="M:Liam.Logging.Formatters.TextLogFormatter.Format(Liam.Logging.Models.LogEvent)">
            <summary>
            格式化日志事件
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>格式化后的字符串</returns>
        </member>
        <member name="M:Liam.Logging.Formatters.TextLogFormatter.FormatToBytes(Liam.Logging.Models.LogEvent)">
            <summary>
            格式化日志事件为字节数组
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>格式化后的字节数组</returns>
        </member>
        <member name="M:Liam.Logging.Formatters.TextLogFormatter.FormatException(System.Exception)">
            <summary>
            格式化异常信息
            </summary>
            <param name="exception">异常对象</param>
            <returns>格式化后的异常信息</returns>
        </member>
        <member name="T:Liam.Logging.Formatters.JsonLogFormatter">
            <summary>
            JSON日志格式化器
            </summary>
        </member>
        <member name="P:Liam.Logging.Formatters.JsonLogFormatter.Name">
            <summary>
            格式化器名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Formatters.JsonLogFormatter.#ctor(System.Text.Json.JsonSerializerOptions)">
            <summary>
            初始化JSON日志格式化器
            </summary>
            <param name="jsonOptions">JSON序列化选项</param>
        </member>
        <member name="M:Liam.Logging.Formatters.JsonLogFormatter.Format(Liam.Logging.Models.LogEvent)">
            <summary>
            格式化日志事件
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>格式化后的字符串</returns>
        </member>
        <member name="M:Liam.Logging.Formatters.JsonLogFormatter.FormatToBytes(Liam.Logging.Models.LogEvent)">
            <summary>
            格式化日志事件为字节数组
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>格式化后的字节数组</returns>
        </member>
        <member name="M:Liam.Logging.Formatters.JsonLogFormatter.FormatExceptionToObject(System.Exception)">
            <summary>
            将异常转换为对象
            </summary>
            <param name="exception">异常对象</param>
            <returns>异常对象字典</returns>
        </member>
        <member name="T:Liam.Logging.Interfaces.ILiamLogger">
            <summary>
            Liam日志记录器接口，扩展了标准的日志功能
            </summary>
        </member>
        <member name="P:Liam.Logging.Interfaces.ILiamLogger.CategoryName">
            <summary>
            日志类别名称
            </summary>
        </member>
        <member name="P:Liam.Logging.Interfaces.ILiamLogger.MinimumLevel">
            <summary>
            最小日志级别
            </summary>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.IsEnabled(Liam.Logging.Constants.LogLevel)">
            <summary>
            判断指定级别的日志是否启用
            </summary>
            <param name="level">日志级别</param>
            <returns>是否启用</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.Log(Liam.Logging.Constants.LogLevel,System.String,System.Exception,System.String,System.String,System.Int32)">
            <summary>
            记录日志
            </summary>
            <param name="level">日志级别</param>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.LogStructured(Liam.Logging.Constants.LogLevel,System.String,System.Object[])">
            <summary>
            记录结构化日志
            </summary>
            <param name="level">日志级别</param>
            <param name="messageTemplate">消息模板</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.LogEvent(Liam.Logging.Models.LogEvent)">
            <summary>
            记录日志事件
            </summary>
            <param name="logEvent">日志事件</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.LogAsync(Liam.Logging.Constants.LogLevel,System.String,System.Exception,System.Threading.CancellationToken,System.String,System.String,System.Int32)">
            <summary>
            异步记录日志
            </summary>
            <param name="level">日志级别</param>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.LogStructuredAsync(Liam.Logging.Constants.LogLevel,System.String,System.Object[],System.Threading.CancellationToken)">
            <summary>
            异步记录结构化日志
            </summary>
            <param name="level">日志级别</param>
            <param name="messageTemplate">消息模板</param>
            <param name="parameters">参数</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.LogEventAsync(Liam.Logging.Models.LogEvent,System.Threading.CancellationToken)">
            <summary>
            异步记录日志事件
            </summary>
            <param name="logEvent">日志事件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.BeginScope``1(``0)">
            <summary>
            创建日志作用域
            </summary>
            <typeparam name="TState">状态类型</typeparam>
            <param name="state">状态对象</param>
            <returns>作用域释放器</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.LogTrace(System.String,System.String,System.String,System.Int32)">
            <summary>
            记录跟踪日志
            </summary>
            <param name="message">日志消息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.LogDebug(System.String,System.String,System.String,System.Int32)">
            <summary>
            记录调试日志
            </summary>
            <param name="message">日志消息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.LogInformation(System.String,System.String,System.String,System.Int32)">
            <summary>
            记录信息日志
            </summary>
            <param name="message">日志消息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.LogWarning(System.String,System.Exception,System.String,System.String,System.Int32)">
            <summary>
            记录警告日志
            </summary>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.LogError(System.String,System.Exception,System.String,System.String,System.Int32)">
            <summary>
            记录错误日志
            </summary>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLogger.LogCritical(System.String,System.Exception,System.String,System.String,System.Int32)">
            <summary>
            记录严重错误日志
            </summary>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="T:Liam.Logging.Interfaces.ILogProvider">
            <summary>
            日志提供程序接口，定义日志输出目标
            </summary>
        </member>
        <member name="P:Liam.Logging.Interfaces.ILogProvider.Name">
            <summary>
            提供程序名称
            </summary>
        </member>
        <member name="P:Liam.Logging.Interfaces.ILogProvider.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Liam.Logging.Interfaces.ILogProvider.SupportsAsync">
            <summary>
            是否支持异步写入
            </summary>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogProvider.InitializeAsync(Liam.Logging.Models.LogProviderConfiguration)">
            <summary>
            初始化提供程序
            </summary>
            <param name="configuration">配置信息</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogProvider.WriteLog(Liam.Logging.Models.LogEvent)">
            <summary>
            写入日志事件
            </summary>
            <param name="logEvent">日志事件</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogProvider.WriteLogAsync(Liam.Logging.Models.LogEvent,System.Threading.CancellationToken)">
            <summary>
            异步写入日志事件
            </summary>
            <param name="logEvent">日志事件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogProvider.WriteLogs(System.Collections.Generic.IEnumerable{Liam.Logging.Models.LogEvent})">
            <summary>
            批量写入日志事件
            </summary>
            <param name="logEvents">日志事件集合</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogProvider.WriteLogsAsync(System.Collections.Generic.IEnumerable{Liam.Logging.Models.LogEvent},System.Threading.CancellationToken)">
            <summary>
            异步批量写入日志事件
            </summary>
            <param name="logEvents">日志事件集合</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogProvider.Flush">
            <summary>
            刷新缓冲区
            </summary>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogProvider.FlushAsync(System.Threading.CancellationToken)">
            <summary>
            异步刷新缓冲区
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="T:Liam.Logging.Interfaces.ILogFormatter">
            <summary>
            日志格式化器接口
            </summary>
        </member>
        <member name="P:Liam.Logging.Interfaces.ILogFormatter.Name">
            <summary>
            格式化器名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogFormatter.Format(Liam.Logging.Models.LogEvent)">
            <summary>
            格式化日志事件
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>格式化后的字符串</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogFormatter.FormatToBytes(Liam.Logging.Models.LogEvent)">
            <summary>
            格式化日志事件为字节数组
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>格式化后的字节数组</returns>
        </member>
        <member name="T:Liam.Logging.Interfaces.ILogFilter">
            <summary>
            日志过滤器接口
            </summary>
        </member>
        <member name="P:Liam.Logging.Interfaces.ILogFilter.Name">
            <summary>
            过滤器名称
            </summary>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogFilter.ShouldLog(Liam.Logging.Models.LogEvent)">
            <summary>
            判断是否应该记录日志
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>是否应该记录</returns>
        </member>
        <member name="T:Liam.Logging.Interfaces.ILogScopeProvider">
            <summary>
            日志作用域提供程序接口
            </summary>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogScopeProvider.Push``1(``0)">
            <summary>
            开始作用域
            </summary>
            <typeparam name="TState">状态类型</typeparam>
            <param name="state">状态对象</param>
            <returns>作用域释放器</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogScopeProvider.GetCurrentScopes">
            <summary>
            获取当前作用域信息
            </summary>
            <returns>作用域信息字典</returns>
        </member>
        <member name="T:Liam.Logging.Interfaces.ILiamLoggerFactory">
            <summary>
            日志工厂接口
            </summary>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLoggerFactory.CreateLogger(System.String)">
            <summary>
            创建日志记录器
            </summary>
            <param name="categoryName">类别名称</param>
            <returns>日志记录器</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLoggerFactory.CreateLogger``1">
            <summary>
            创建泛型日志记录器
            </summary>
            <typeparam name="T">类型</typeparam>
            <returns>日志记录器</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLoggerFactory.AddProvider(Liam.Logging.Interfaces.ILogProvider)">
            <summary>
            添加日志提供程序
            </summary>
            <param name="provider">日志提供程序</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLoggerFactory.RemoveProvider(Liam.Logging.Interfaces.ILogProvider)">
            <summary>
            移除日志提供程序
            </summary>
            <param name="provider">日志提供程序</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLoggerFactory.GetProviders">
            <summary>
            获取所有日志提供程序
            </summary>
            <returns>日志提供程序集合</returns>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLoggerFactory.Flush">
            <summary>
            刷新所有提供程序
            </summary>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILiamLoggerFactory.FlushAsync(System.Threading.CancellationToken)">
            <summary>
            异步刷新所有提供程序
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="T:Liam.Logging.Interfaces.ILiamLogger`1">
            <summary>
            泛型日志记录器接口
            </summary>
            <typeparam name="T">类型</typeparam>
        </member>
        <member name="T:Liam.Logging.Interfaces.ILogPerformanceMonitor">
            <summary>
            日志性能监控接口
            </summary>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogPerformanceMonitor.RecordWritePerformance(System.String,System.TimeSpan,System.Int32)">
            <summary>
            记录日志写入性能
            </summary>
            <param name="providerName">提供程序名称</param>
            <param name="duration">耗时</param>
            <param name="logCount">日志数量</param>
        </member>
        <member name="M:Liam.Logging.Interfaces.ILogPerformanceMonitor.GetPerformanceStats">
            <summary>
            获取性能统计信息
            </summary>
            <returns>性能统计信息</returns>
        </member>
        <member name="T:Liam.Logging.Models.LogConfiguration">
            <summary>
            日志配置类
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.MinimumLevel">
            <summary>
            最小日志级别
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.EnableAsync">
            <summary>
            是否启用异步日志记录
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.AsyncQueueSize">
            <summary>
            异步队列大小
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.BatchSize">
            <summary>
            批处理大小
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.BatchTimeoutMs">
            <summary>
            批处理超时时间（毫秒）
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.IncludeScopes">
            <summary>
            是否包含作用域信息
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.IncludeExceptionDetails">
            <summary>
            是否包含异常详细信息
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.IncludeSourceInfo">
            <summary>
            是否包含源代码信息
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.ApplicationName">
            <summary>
            应用程序名称
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.Environment">
            <summary>
            环境名称
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.GlobalProperties">
            <summary>
            自定义属性
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.Providers">
            <summary>
            日志提供程序配置
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogConfiguration.Filters">
            <summary>
            日志过滤器配置
            </summary>
        </member>
        <member name="T:Liam.Logging.Models.LogProviderConfiguration">
            <summary>
            日志提供程序配置
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogProviderConfiguration.TypeName">
            <summary>
            提供程序类型名称
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogProviderConfiguration.Enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogProviderConfiguration.MinimumLevel">
            <summary>
            最小日志级别
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogProviderConfiguration.Settings">
            <summary>
            配置参数
            </summary>
        </member>
        <member name="T:Liam.Logging.Models.LogFilterConfiguration">
            <summary>
            日志过滤器配置
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogFilterConfiguration.Type">
            <summary>
            过滤器类型
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogFilterConfiguration.Condition">
            <summary>
            过滤条件
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogFilterConfiguration.Action">
            <summary>
            过滤动作
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogFilterConfiguration.Settings">
            <summary>
            配置参数
            </summary>
        </member>
        <member name="T:Liam.Logging.Models.LogFilterType">
            <summary>
            日志过滤器类型
            </summary>
        </member>
        <member name="F:Liam.Logging.Models.LogFilterType.Level">
            <summary>
            按级别过滤
            </summary>
        </member>
        <member name="F:Liam.Logging.Models.LogFilterType.Category">
            <summary>
            按类别过滤
            </summary>
        </member>
        <member name="F:Liam.Logging.Models.LogFilterType.Message">
            <summary>
            按消息内容过滤
            </summary>
        </member>
        <member name="F:Liam.Logging.Models.LogFilterType.Property">
            <summary>
            按属性过滤
            </summary>
        </member>
        <member name="F:Liam.Logging.Models.LogFilterType.Custom">
            <summary>
            自定义过滤器
            </summary>
        </member>
        <member name="T:Liam.Logging.Models.LogFilterAction">
            <summary>
            日志过滤动作
            </summary>
        </member>
        <member name="F:Liam.Logging.Models.LogFilterAction.Include">
            <summary>
            包含（记录日志）
            </summary>
        </member>
        <member name="F:Liam.Logging.Models.LogFilterAction.Exclude">
            <summary>
            排除（不记录日志）
            </summary>
        </member>
        <member name="T:Liam.Logging.Models.FileLogConfiguration">
            <summary>
            文件日志配置
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.FileLogConfiguration.FilePath">
            <summary>
            日志文件路径
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.FileLogConfiguration.EnableRotation">
            <summary>
            是否启用日志轮转
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.FileLogConfiguration.MaxFileSize">
            <summary>
            最大文件大小（字节）
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.FileLogConfiguration.RetainedFileCount">
            <summary>
            保留文件数量
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.FileLogConfiguration.RotationInterval">
            <summary>
            日志轮转间隔
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.FileLogConfiguration.Encoding">
            <summary>
            文件编码
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.FileLogConfiguration.AutoFlush">
            <summary>
            是否自动刷新
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.FileLogConfiguration.BufferSize">
            <summary>
            缓冲区大小
            </summary>
        </member>
        <member name="T:Liam.Logging.Models.ConsoleLogConfiguration">
            <summary>
            控制台日志配置
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.ConsoleLogConfiguration.EnableColors">
            <summary>
            是否启用颜色输出
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.ConsoleLogConfiguration.UseStandardError">
            <summary>
            是否输出到标准错误流
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.ConsoleLogConfiguration.TimestampFormat">
            <summary>
            时间戳格式
            </summary>
        </member>
        <member name="T:Liam.Logging.Models.LogEvent">
            <summary>
            日志事件模型，包含完整的日志信息
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.Id">
            <summary>
            日志事件的唯一标识符
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.Level">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.Message">
            <summary>
            日志消息
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.MessageTemplate">
            <summary>
            日志模板（用于结构化日志）
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.Parameters">
            <summary>
            日志参数（用于结构化日志）
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.Exception">
            <summary>
            异常信息
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.Timestamp">
            <summary>
            日志时间戳
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.Category">
            <summary>
            日志类别（通常是类名）
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.EventId">
            <summary>
            事件ID
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.EventName">
            <summary>
            事件名称
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.ThreadId">
            <summary>
            线程ID
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.ProcessId">
            <summary>
            进程ID
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.MachineName">
            <summary>
            机器名称
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.ApplicationName">
            <summary>
            应用程序名称
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.Scopes">
            <summary>
            日志作用域信息
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.Properties">
            <summary>
            自定义属性
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogEvent.Source">
            <summary>
            日志来源信息
            </summary>
        </member>
        <member name="M:Liam.Logging.Models.LogEvent.Clone">
            <summary>
            创建日志事件的副本
            </summary>
            <returns>日志事件副本</returns>
        </member>
        <member name="T:Liam.Logging.Models.LogSource">
            <summary>
            日志来源信息
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogSource.FilePath">
            <summary>
            源文件路径
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogSource.LineNumber">
            <summary>
            行号
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogSource.MethodName">
            <summary>
            方法名
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogSource.ClassName">
            <summary>
            类名
            </summary>
        </member>
        <member name="P:Liam.Logging.Models.LogSource.Namespace">
            <summary>
            命名空间
            </summary>
        </member>
        <member name="M:Liam.Logging.Models.LogSource.Clone">
            <summary>
            创建日志来源信息的副本
            </summary>
            <returns>日志来源信息副本</returns>
        </member>
        <member name="T:Liam.Logging.Providers.ConsoleLogProvider">
            <summary>
            控制台日志提供程序
            </summary>
        </member>
        <member name="P:Liam.Logging.Providers.ConsoleLogProvider.Name">
            <summary>
            提供程序名称
            </summary>
        </member>
        <member name="P:Liam.Logging.Providers.ConsoleLogProvider.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Liam.Logging.Providers.ConsoleLogProvider.SupportsAsync">
            <summary>
            是否支持异步写入
            </summary>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.#ctor(Liam.Logging.Interfaces.ILogFormatter,System.Boolean,System.Boolean)">
            <summary>
            初始化控制台日志提供程序
            </summary>
            <param name="formatter">日志格式化器</param>
            <param name="enableColors">是否启用颜色输出</param>
            <param name="useStandardError">是否输出到标准错误流</param>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.InitializeAsync(Liam.Logging.Models.LogProviderConfiguration)">
            <summary>
            初始化提供程序
            </summary>
            <param name="configuration">配置信息</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.WriteLog(Liam.Logging.Models.LogEvent)">
            <summary>
            写入日志事件
            </summary>
            <param name="logEvent">日志事件</param>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.WriteLogAsync(Liam.Logging.Models.LogEvent,System.Threading.CancellationToken)">
            <summary>
            异步写入日志事件
            </summary>
            <param name="logEvent">日志事件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.WriteLogs(System.Collections.Generic.IEnumerable{Liam.Logging.Models.LogEvent})">
            <summary>
            批量写入日志事件
            </summary>
            <param name="logEvents">日志事件集合</param>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.WriteLogsAsync(System.Collections.Generic.IEnumerable{Liam.Logging.Models.LogEvent},System.Threading.CancellationToken)">
            <summary>
            异步批量写入日志事件
            </summary>
            <param name="logEvents">日志事件集合</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.Flush">
            <summary>
            刷新缓冲区
            </summary>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.FlushAsync(System.Threading.CancellationToken)">
            <summary>
            异步刷新缓冲区
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.Dispose(System.Boolean)">
            <summary>
            释放资源
            </summary>
            <param name="disposing">是否正在释放托管资源</param>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.ShouldUseErrorStream(Liam.Logging.Constants.LogLevel)">
            <summary>
            判断是否应该使用错误流
            </summary>
            <param name="level">日志级别</param>
            <returns>是否使用错误流</returns>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.SupportsColors">
            <summary>
            判断是否支持颜色输出
            </summary>
            <returns>是否支持颜色</returns>
        </member>
        <member name="M:Liam.Logging.Providers.ConsoleLogProvider.WriteWithColor(System.IO.TextWriter,System.String,Liam.Logging.Constants.LogLevel)">
            <summary>
            带颜色写入文本
            </summary>
            <param name="writer">文本写入器</param>
            <param name="message">消息</param>
            <param name="level">日志级别</param>
        </member>
        <member name="T:Liam.Logging.Providers.FileLogProvider">
            <summary>
            文件日志提供程序
            </summary>
        </member>
        <member name="P:Liam.Logging.Providers.FileLogProvider.Name">
            <summary>
            提供程序名称
            </summary>
        </member>
        <member name="P:Liam.Logging.Providers.FileLogProvider.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Liam.Logging.Providers.FileLogProvider.SupportsAsync">
            <summary>
            是否支持异步写入
            </summary>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.#ctor(System.String,Liam.Logging.Interfaces.ILogFormatter,System.Boolean,System.Int64,System.Int32,System.Nullable{System.TimeSpan},System.Text.Encoding,System.Boolean,System.Int32)">
            <summary>
            初始化文件日志提供程序
            </summary>
            <param name="filePath">文件路径</param>
            <param name="formatter">日志格式化器</param>
            <param name="enableRotation">是否启用日志轮转</param>
            <param name="maxFileSize">最大文件大小（字节）</param>
            <param name="retainedFileCount">保留文件数量</param>
            <param name="rotationInterval">轮转间隔</param>
            <param name="encoding">文件编码</param>
            <param name="autoFlush">是否自动刷新</param>
            <param name="bufferSize">缓冲区大小</param>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.InitializeAsync(Liam.Logging.Models.LogProviderConfiguration)">
            <summary>
            初始化提供程序
            </summary>
            <param name="configuration">配置信息</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.WriteLog(Liam.Logging.Models.LogEvent)">
            <summary>
            写入日志事件
            </summary>
            <param name="logEvent">日志事件</param>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.WriteLogAsync(Liam.Logging.Models.LogEvent,System.Threading.CancellationToken)">
            <summary>
            异步写入日志事件
            </summary>
            <param name="logEvent">日志事件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.WriteLogs(System.Collections.Generic.IEnumerable{Liam.Logging.Models.LogEvent})">
            <summary>
            批量写入日志事件
            </summary>
            <param name="logEvents">日志事件集合</param>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.WriteLogsAsync(System.Collections.Generic.IEnumerable{Liam.Logging.Models.LogEvent},System.Threading.CancellationToken)">
            <summary>
            异步批量写入日志事件
            </summary>
            <param name="logEvents">日志事件集合</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.Flush">
            <summary>
            刷新缓冲区
            </summary>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.FlushAsync(System.Threading.CancellationToken)">
            <summary>
            异步刷新缓冲区
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.Dispose(System.Boolean)">
            <summary>
            释放资源
            </summary>
            <param name="disposing">是否正在释放托管资源</param>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.DisposeAsync">
            <summary>
            异步释放资源
            </summary>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.EnsureFileStream">
            <summary>
            确保文件流已创建
            </summary>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.EnsureFileStreamAsync">
            <summary>
            异步确保文件流已创建
            </summary>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.CreateFileStream">
            <summary>
            创建文件流
            </summary>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.GetCurrentFilePath">
            <summary>
            获取当前文件路径
            </summary>
            <returns>文件路径</returns>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.CheckRotation">
            <summary>
            检查是否需要轮转
            </summary>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.RotateFile">
            <summary>
            轮转文件
            </summary>
        </member>
        <member name="M:Liam.Logging.Providers.FileLogProvider.CleanupOldFiles">
            <summary>
            清理旧文件
            </summary>
        </member>
        <member name="T:Liam.Logging.Services.LiamLogger">
            <summary>
            Liam日志记录器实现
            </summary>
        </member>
        <member name="P:Liam.Logging.Services.LiamLogger.CategoryName">
            <summary>
            日志类别名称
            </summary>
        </member>
        <member name="P:Liam.Logging.Services.LiamLogger.MinimumLevel">
            <summary>
            最小日志级别
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.#ctor(System.String,Liam.Logging.Interfaces.ILiamLoggerFactory,Liam.Logging.Interfaces.ILogScopeProvider,System.Collections.Generic.IEnumerable{Liam.Logging.Interfaces.ILogFilter})">
            <summary>
            初始化日志记录器
            </summary>
            <param name="categoryName">类别名称</param>
            <param name="factory">日志工厂</param>
            <param name="scopeProvider">作用域提供程序</param>
            <param name="filters">过滤器集合</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.IsEnabled(Liam.Logging.Constants.LogLevel)">
            <summary>
            判断指定级别的日志是否启用
            </summary>
            <param name="level">日志级别</param>
            <returns>是否启用</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.Log(Liam.Logging.Constants.LogLevel,System.String,System.Exception,System.String,System.String,System.Int32)">
            <summary>
            记录日志
            </summary>
            <param name="level">日志级别</param>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.LogStructured(Liam.Logging.Constants.LogLevel,System.String,System.Object[])">
            <summary>
            记录结构化日志
            </summary>
            <param name="level">日志级别</param>
            <param name="messageTemplate">消息模板</param>
            <param name="parameters">参数</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.LogEvent(Liam.Logging.Models.LogEvent)">
            <summary>
            记录日志事件
            </summary>
            <param name="logEvent">日志事件</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.LogAsync(Liam.Logging.Constants.LogLevel,System.String,System.Exception,System.Threading.CancellationToken,System.String,System.String,System.Int32)">
            <summary>
            异步记录日志
            </summary>
            <param name="level">日志级别</param>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.LogStructuredAsync(Liam.Logging.Constants.LogLevel,System.String,System.Object[],System.Threading.CancellationToken)">
            <summary>
            异步记录结构化日志
            </summary>
            <param name="level">日志级别</param>
            <param name="messageTemplate">消息模板</param>
            <param name="parameters">参数</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.LogEventAsync(Liam.Logging.Models.LogEvent,System.Threading.CancellationToken)">
            <summary>
            异步记录日志事件
            </summary>
            <param name="logEvent">日志事件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.BeginScope``1(``0)">
            <summary>
            创建日志作用域
            </summary>
            <typeparam name="TState">状态类型</typeparam>
            <param name="state">状态对象</param>
            <returns>作用域释放器</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.LogTrace(System.String,System.String,System.String,System.Int32)">
            <summary>
            记录跟踪日志
            </summary>
            <param name="message">日志消息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.LogDebug(System.String,System.String,System.String,System.Int32)">
            <summary>
            记录调试日志
            </summary>
            <param name="message">日志消息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.LogInformation(System.String,System.String,System.String,System.Int32)">
            <summary>
            记录信息日志
            </summary>
            <param name="message">日志消息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.LogWarning(System.String,System.Exception,System.String,System.String,System.Int32)">
            <summary>
            记录警告日志
            </summary>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.LogError(System.String,System.Exception,System.String,System.String,System.Int32)">
            <summary>
            记录错误日志
            </summary>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.LogCritical(System.String,System.Exception,System.String,System.String,System.Int32)">
            <summary>
            记录严重错误日志
            </summary>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.CreateLogEvent(Liam.Logging.Constants.LogLevel,System.String,System.Exception,System.String,System.String,System.Int32)">
            <summary>
            创建日志事件
            </summary>
            <param name="level">日志级别</param>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
            <param name="memberName">调用成员名称</param>
            <param name="sourceFilePath">源文件路径</param>
            <param name="sourceLineNumber">源代码行号</param>
            <returns>日志事件</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.ShouldLog(Liam.Logging.Models.LogEvent)">
            <summary>
            判断是否应该记录日志
            </summary>
            <param name="logEvent">日志事件</param>
            <returns>是否应该记录</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.WriteToProviders(Liam.Logging.Models.LogEvent)">
            <summary>
            写入到所有提供程序
            </summary>
            <param name="logEvent">日志事件</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.WriteToProvidersAsync(Liam.Logging.Models.LogEvent,System.Threading.CancellationToken)">
            <summary>
            异步写入到所有提供程序
            </summary>
            <param name="logEvent">日志事件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger.FormatMessage(System.String,System.Object[])">
            <summary>
            格式化消息
            </summary>
            <param name="messageTemplate">消息模板</param>
            <param name="parameters">参数</param>
            <returns>格式化后的消息</returns>
        </member>
        <member name="T:Liam.Logging.Services.LiamLogger`1">
            <summary>
            泛型日志记录器实现
            </summary>
            <typeparam name="T">类型</typeparam>
        </member>
        <member name="M:Liam.Logging.Services.LiamLogger`1.#ctor(Liam.Logging.Interfaces.ILiamLoggerFactory,Liam.Logging.Interfaces.ILogScopeProvider,System.Collections.Generic.IEnumerable{Liam.Logging.Interfaces.ILogFilter})">
            <summary>
            初始化泛型日志记录器
            </summary>
            <param name="factory">日志工厂</param>
            <param name="scopeProvider">作用域提供程序</param>
            <param name="filters">过滤器集合</param>
        </member>
        <member name="T:Liam.Logging.Services.LiamLoggerFactory">
            <summary>
            Liam日志工厂实现
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.#ctor(Liam.Logging.Models.LogConfiguration,Liam.Logging.Interfaces.ILogScopeProvider,Liam.Logging.Interfaces.ILogPerformanceMonitor)">
            <summary>
            初始化日志工厂
            </summary>
            <param name="configuration">日志配置</param>
            <param name="scopeProvider">作用域提供程序</param>
            <param name="performanceMonitor">性能监控器</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.CreateLogger(System.String)">
            <summary>
            创建日志记录器
            </summary>
            <param name="categoryName">类别名称</param>
            <returns>日志记录器</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.CreateLogger``1">
            <summary>
            创建泛型日志记录器
            </summary>
            <typeparam name="T">类型</typeparam>
            <returns>日志记录器</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.AddProvider(Liam.Logging.Interfaces.ILogProvider)">
            <summary>
            添加日志提供程序
            </summary>
            <param name="provider">日志提供程序</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.RemoveProvider(Liam.Logging.Interfaces.ILogProvider)">
            <summary>
            移除日志提供程序
            </summary>
            <param name="provider">日志提供程序</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.GetProviders">
            <summary>
            获取所有日志提供程序
            </summary>
            <returns>日志提供程序集合</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.AddFilter(Liam.Logging.Interfaces.ILogFilter)">
            <summary>
            添加日志过滤器
            </summary>
            <param name="filter">日志过滤器</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.RemoveFilter(Liam.Logging.Interfaces.ILogFilter)">
            <summary>
            移除日志过滤器
            </summary>
            <param name="filter">日志过滤器</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.GetFilters">
            <summary>
            获取所有日志过滤器
            </summary>
            <returns>日志过滤器集合</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.Flush">
            <summary>
            刷新所有提供程序
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.FlushAsync(System.Threading.CancellationToken)">
            <summary>
            异步刷新所有提供程序
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.GetPerformanceStats">
            <summary>
            获取性能统计信息
            </summary>
            <returns>性能统计信息</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.GetFactoryStats">
            <summary>
            获取工厂统计信息
            </summary>
            <returns>工厂统计信息</returns>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.Reconfigure(Liam.Logging.Models.LogConfiguration)">
            <summary>
            重新配置工厂
            </summary>
            <param name="configuration">新的配置</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.ClearLoggers">
            <summary>
            清除所有缓存的日志记录器
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.Dispose(System.Boolean)">
            <summary>
            释放资源
            </summary>
            <param name="disposing">是否正在释放托管资源</param>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.LiamLoggerFactory.DisposeAsync">
            <summary>
            异步释放资源
            </summary>
            <returns>异步任务</returns>
        </member>
        <member name="T:Liam.Logging.Services.LogPerformanceMonitor">
            <summary>
            日志性能监控器实现
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.LogPerformanceMonitor.RecordWritePerformance(System.String,System.TimeSpan,System.Int32)">
            <summary>
            记录日志写入性能
            </summary>
            <param name="providerName">提供程序名称</param>
            <param name="duration">耗时</param>
            <param name="logCount">日志数量</param>
        </member>
        <member name="M:Liam.Logging.Services.LogPerformanceMonitor.GetPerformanceStats">
            <summary>
            获取性能统计信息
            </summary>
            <returns>性能统计信息</returns>
        </member>
        <member name="M:Liam.Logging.Services.LogPerformanceMonitor.Reset">
            <summary>
            重置性能统计
            </summary>
        </member>
        <member name="T:Liam.Logging.Services.LogPerformanceMonitor.ProviderPerformanceStats">
            <summary>
            提供程序性能统计
            </summary>
        </member>
        <member name="T:Liam.Logging.Services.NullLogPerformanceMonitor">
            <summary>
            空的性能监控器（用于禁用性能监控）
            </summary>
        </member>
        <member name="F:Liam.Logging.Services.NullLogPerformanceMonitor.Instance">
            <summary>
            单例实例
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.NullLogPerformanceMonitor.RecordWritePerformance(System.String,System.TimeSpan,System.Int32)">
            <summary>
            记录日志写入性能（空实现）
            </summary>
            <param name="providerName">提供程序名称</param>
            <param name="duration">耗时</param>
            <param name="logCount">日志数量</param>
        </member>
        <member name="M:Liam.Logging.Services.NullLogPerformanceMonitor.GetPerformanceStats">
            <summary>
            获取性能统计信息（空实现）
            </summary>
            <returns>空的统计信息</returns>
        </member>
        <member name="T:Liam.Logging.Services.PerformanceMonitorExtensions">
            <summary>
            性能监控扩展方法
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.PerformanceMonitorExtensions.MeasureAndRecord(Liam.Logging.Interfaces.ILogPerformanceMonitor,System.String,System.Action,System.Int32)">
            <summary>
            测量执行时间并记录性能
            </summary>
            <param name="monitor">性能监控器</param>
            <param name="providerName">提供程序名称</param>
            <param name="action">要执行的操作</param>
            <param name="logCount">日志数量</param>
        </member>
        <member name="M:Liam.Logging.Services.PerformanceMonitorExtensions.MeasureAndRecordAsync(Liam.Logging.Interfaces.ILogPerformanceMonitor,System.String,System.Func{System.Threading.Tasks.Task},System.Int32)">
            <summary>
            异步测量执行时间并记录性能
            </summary>
            <param name="monitor">性能监控器</param>
            <param name="providerName">提供程序名称</param>
            <param name="asyncAction">要执行的异步操作</param>
            <param name="logCount">日志数量</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:Liam.Logging.Services.PerformanceMonitorExtensions.MeasureAndRecord``1(Liam.Logging.Interfaces.ILogPerformanceMonitor,System.String,System.Func{``0},System.Int32)">
            <summary>
            测量执行时间并记录性能（带返回值）
            </summary>
            <typeparam name="T">返回值类型</typeparam>
            <param name="monitor">性能监控器</param>
            <param name="providerName">提供程序名称</param>
            <param name="func">要执行的函数</param>
            <param name="logCount">日志数量</param>
            <returns>函数返回值</returns>
        </member>
        <member name="M:Liam.Logging.Services.PerformanceMonitorExtensions.MeasureAndRecordAsync``1(Liam.Logging.Interfaces.ILogPerformanceMonitor,System.String,System.Func{System.Threading.Tasks.Task{``0}},System.Int32)">
            <summary>
            异步测量执行时间并记录性能（带返回值）
            </summary>
            <typeparam name="T">返回值类型</typeparam>
            <param name="monitor">性能监控器</param>
            <param name="providerName">提供程序名称</param>
            <param name="asyncFunc">要执行的异步函数</param>
            <param name="logCount">日志数量</param>
            <returns>异步任务</returns>
        </member>
        <member name="T:Liam.Logging.Services.LogScopeProvider">
            <summary>
            日志作用域提供程序实现
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.LogScopeProvider.Push``1(``0)">
            <summary>
            开始作用域
            </summary>
            <typeparam name="TState">状态类型</typeparam>
            <param name="state">状态对象</param>
            <returns>作用域释放器</returns>
        </member>
        <member name="M:Liam.Logging.Services.LogScopeProvider.GetCurrentScopes">
            <summary>
            获取当前作用域信息
            </summary>
            <returns>作用域信息字典</returns>
        </member>
        <member name="T:Liam.Logging.Services.LogScopeProvider.LogScope">
            <summary>
            日志作用域实现
            </summary>
        </member>
        <member name="P:Liam.Logging.Services.LogScopeProvider.LogScope.State">
            <summary>
            状态对象
            </summary>
        </member>
        <member name="P:Liam.Logging.Services.LogScopeProvider.LogScope.Parent">
            <summary>
            父作用域
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.LogScopeProvider.LogScope.#ctor(System.Object,Liam.Logging.Services.LogScopeProvider.LogScope)">
            <summary>
            初始化日志作用域
            </summary>
            <param name="state">状态对象</param>
            <param name="parent">父作用域</param>
        </member>
        <member name="M:Liam.Logging.Services.LogScopeProvider.LogScope.Dispose">
            <summary>
            释放作用域
            </summary>
        </member>
        <member name="T:Liam.Logging.Services.ThreadSafeLogScopeProvider">
            <summary>
            线程安全的日志作用域提供程序
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.ThreadSafeLogScopeProvider.Push``1(``0)">
            <summary>
            开始作用域
            </summary>
            <typeparam name="TState">状态类型</typeparam>
            <param name="state">状态对象</param>
            <returns>作用域释放器</returns>
        </member>
        <member name="M:Liam.Logging.Services.ThreadSafeLogScopeProvider.GetCurrentScopes">
            <summary>
            获取当前作用域信息
            </summary>
            <returns>作用域信息字典</returns>
        </member>
        <member name="T:Liam.Logging.Services.ThreadSafeLogScopeProvider.LogScopeStack">
            <summary>
            线程作用域栈
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.ThreadSafeLogScopeProvider.LogScopeStack.Push(System.Object)">
            <summary>
            推入作用域
            </summary>
            <param name="state">状态对象</param>
            <returns>作用域释放器</returns>
        </member>
        <member name="M:Liam.Logging.Services.ThreadSafeLogScopeProvider.LogScopeStack.Pop">
            <summary>
            弹出作用域
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.ThreadSafeLogScopeProvider.LogScopeStack.GetCurrentScopes">
            <summary>
            获取当前作用域信息
            </summary>
            <returns>作用域信息字典</returns>
        </member>
        <member name="T:Liam.Logging.Services.ThreadSafeLogScopeProvider.LogScopeStack.ScopeDisposer">
            <summary>
            作用域释放器
            </summary>
        </member>
        <member name="T:Liam.Logging.Services.NullLogScopeProvider">
            <summary>
            空的日志作用域提供程序（用于禁用作用域功能）
            </summary>
        </member>
        <member name="F:Liam.Logging.Services.NullLogScopeProvider.Instance">
            <summary>
            单例实例
            </summary>
        </member>
        <member name="M:Liam.Logging.Services.NullLogScopeProvider.Push``1(``0)">
            <summary>
            开始作用域（空实现）
            </summary>
            <typeparam name="TState">状态类型</typeparam>
            <param name="state">状态对象</param>
            <returns>空的释放器</returns>
        </member>
        <member name="M:Liam.Logging.Services.NullLogScopeProvider.GetCurrentScopes">
            <summary>
            获取当前作用域信息（空实现）
            </summary>
            <returns>空的字典</returns>
        </member>
        <member name="T:Liam.Logging.Services.NullLogScopeProvider.NullDisposable">
            <summary>
            空的释放器
            </summary>
        </member>
    </members>
</doc>
