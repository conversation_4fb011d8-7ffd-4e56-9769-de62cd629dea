using Liam.Logging.Constants;
using Liam.Logging.Interfaces;
using Liam.Logging.Models;
using System.Runtime.CompilerServices;

namespace Liam.Logging.Services;

/// <summary>
/// Liam日志记录器实现
/// </summary>
public class LiamLogger : ILiamLogger
{
    private readonly ILiamLoggerFactory _factory;
    private readonly ILogScopeProvider _scopeProvider;
    private readonly List<ILogFilter> _filters;

    /// <summary>
    /// 日志类别名称
    /// </summary>
    public string CategoryName { get; }

    /// <summary>
    /// 最小日志级别
    /// </summary>
    public LogLevel MinimumLevel { get; set; } = LogLevel.Information;

    /// <summary>
    /// 初始化日志记录器
    /// </summary>
    /// <param name="categoryName">类别名称</param>
    /// <param name="factory">日志工厂</param>
    /// <param name="scopeProvider">作用域提供程序</param>
    /// <param name="filters">过滤器集合</param>
    public LiamLogger(
        string categoryName,
        ILiamLoggerFactory factory,
        ILogScopeProvider scopeProvider,
        IEnumerable<ILogFilter>? filters = null)
    {
        CategoryName = categoryName ?? throw new ArgumentNullException(nameof(categoryName));
        _factory = factory ?? throw new ArgumentNullException(nameof(factory));
        _scopeProvider = scopeProvider ?? throw new ArgumentNullException(nameof(scopeProvider));
        _filters = filters?.ToList() ?? new List<ILogFilter>();
    }

    /// <summary>
    /// 判断指定级别的日志是否启用
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <returns>是否启用</returns>
    public bool IsEnabled(LogLevel level)
    {
        return LogLevelConstants.IsEnabled(level, MinimumLevel);
    }

    /// <summary>
    /// 记录日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    public void Log(LogLevel level, string message, Exception? exception = null,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        if (!IsEnabled(level))
            return;

        var logEvent = CreateLogEvent(level, message, exception, memberName, sourceFilePath, sourceLineNumber);
        
        if (ShouldLog(logEvent))
        {
            WriteToProviders(logEvent);
        }
    }

    /// <summary>
    /// 记录结构化日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="messageTemplate">消息模板</param>
    /// <param name="parameters">参数</param>
    public void LogStructured(LogLevel level, string messageTemplate, params object[] parameters)
    {
        if (!IsEnabled(level))
            return;

        var message = FormatMessage(messageTemplate, parameters);
        var logEvent = CreateLogEvent(level, message);
        logEvent.MessageTemplate = messageTemplate;
        logEvent.Parameters = parameters;

        if (ShouldLog(logEvent))
        {
            WriteToProviders(logEvent);
        }
    }

    /// <summary>
    /// 记录日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    public void LogEvent(LogEvent logEvent)
    {
        if (!IsEnabled(logEvent.Level))
            return;

        if (ShouldLog(logEvent))
        {
            WriteToProviders(logEvent);
        }
    }

    /// <summary>
    /// 异步记录日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    /// <returns>异步任务</returns>
    public async Task LogAsync(LogLevel level, string message, Exception? exception = null,
        CancellationToken cancellationToken = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        if (!IsEnabled(level))
            return;

        var logEvent = CreateLogEvent(level, message, exception, memberName, sourceFilePath, sourceLineNumber);
        
        if (ShouldLog(logEvent))
        {
            await WriteToProvidersAsync(logEvent, cancellationToken);
        }
    }

    /// <summary>
    /// 异步记录结构化日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="messageTemplate">消息模板</param>
    /// <param name="parameters">参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public async Task LogStructuredAsync(LogLevel level, string messageTemplate, object[] parameters,
        CancellationToken cancellationToken = default)
    {
        if (!IsEnabled(level))
            return;

        var message = FormatMessage(messageTemplate, parameters);
        var logEvent = CreateLogEvent(level, message);
        logEvent.MessageTemplate = messageTemplate;
        logEvent.Parameters = parameters;

        if (ShouldLog(logEvent))
        {
            await WriteToProvidersAsync(logEvent, cancellationToken);
        }
    }

    /// <summary>
    /// 异步记录日志事件
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public async Task LogEventAsync(LogEvent logEvent, CancellationToken cancellationToken = default)
    {
        if (!IsEnabled(logEvent.Level))
            return;

        if (ShouldLog(logEvent))
        {
            await WriteToProvidersAsync(logEvent, cancellationToken);
        }
    }

    /// <summary>
    /// 创建日志作用域
    /// </summary>
    /// <typeparam name="TState">状态类型</typeparam>
    /// <param name="state">状态对象</param>
    /// <returns>作用域释放器</returns>
    public IDisposable BeginScope<TState>(TState state) where TState : notnull
    {
        return _scopeProvider.Push(state);
    }

    /// <summary>
    /// 记录跟踪日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    public void LogTrace(string message,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        Log(LogLevel.Trace, message, null, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 记录调试日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    public void LogDebug(string message,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        Log(LogLevel.Debug, message, null, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 记录信息日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    public void LogInformation(string message,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        Log(LogLevel.Information, message, null, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 记录警告日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    public void LogWarning(string message, Exception? exception = null,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        Log(LogLevel.Warning, message, exception, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 记录错误日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    public void LogError(string message, Exception? exception = null,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        Log(LogLevel.Error, message, exception, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 记录严重错误日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    public void LogCritical(string message, Exception? exception = null,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        Log(LogLevel.Critical, message, exception, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 创建日志事件
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    /// <returns>日志事件</returns>
    private LogEvent CreateLogEvent(LogLevel level, string message, Exception? exception = null,
        string memberName = "", string sourceFilePath = "", int sourceLineNumber = 0)
    {
        var logEvent = new LogEvent
        {
            Level = level,
            Message = message,
            Exception = exception,
            Category = CategoryName,
            Timestamp = DateTimeOffset.Now,
            Scopes = _scopeProvider.GetCurrentScopes()
        };

        // 设置源代码信息
        if (!string.IsNullOrEmpty(memberName) || !string.IsNullOrEmpty(sourceFilePath) || sourceLineNumber > 0)
        {
            logEvent.Source = new LogSource
            {
                MethodName = memberName,
                FilePath = sourceFilePath,
                LineNumber = sourceLineNumber > 0 ? sourceLineNumber : null,
                ClassName = CategoryName
            };
        }

        return logEvent;
    }

    /// <summary>
    /// 判断是否应该记录日志
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <returns>是否应该记录</returns>
    private bool ShouldLog(LogEvent logEvent)
    {
        return _filters.All(filter => filter.ShouldLog(logEvent));
    }

    /// <summary>
    /// 写入到所有提供程序
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    private void WriteToProviders(LogEvent logEvent)
    {
        var providers = _factory.GetProviders();
        foreach (var provider in providers.Where(p => p.IsEnabled))
        {
            try
            {
                provider.WriteLog(logEvent);
            }
            catch
            {
                // 忽略提供程序写入失败，避免影响应用程序
            }
        }
    }

    /// <summary>
    /// 异步写入到所有提供程序
    /// </summary>
    /// <param name="logEvent">日志事件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    private async Task WriteToProvidersAsync(LogEvent logEvent, CancellationToken cancellationToken)
    {
        var providers = _factory.GetProviders();
        var tasks = providers
            .Where(p => p.IsEnabled)
            .Select(async provider =>
            {
                try
                {
                    await provider.WriteLogAsync(logEvent, cancellationToken);
                }
                catch
                {
                    // 忽略提供程序写入失败，避免影响应用程序
                }
            });

        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// 格式化消息
    /// </summary>
    /// <param name="messageTemplate">消息模板</param>
    /// <param name="parameters">参数</param>
    /// <returns>格式化后的消息</returns>
    private static string FormatMessage(string messageTemplate, object[] parameters)
    {
        try
        {
            return string.Format(messageTemplate, parameters);
        }
        catch
        {
            return messageTemplate;
        }
    }
}

/// <summary>
/// 泛型日志记录器实现
/// </summary>
/// <typeparam name="T">类型</typeparam>
public class LiamLogger<T> : LiamLogger, ILiamLogger<T>
{
    /// <summary>
    /// 初始化泛型日志记录器
    /// </summary>
    /// <param name="factory">日志工厂</param>
    /// <param name="scopeProvider">作用域提供程序</param>
    /// <param name="filters">过滤器集合</param>
    public LiamLogger(
        ILiamLoggerFactory factory,
        ILogScopeProvider scopeProvider,
        IEnumerable<ILogFilter>? filters = null)
        : base(typeof(T).FullName ?? typeof(T).Name, factory, scopeProvider, filters)
    {
    }
}
