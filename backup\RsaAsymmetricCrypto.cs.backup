using System.Security.Cryptography;
using System.Text;
using Liam.Cryptography.Constants;
using Liam.Cryptography.Exceptions;
using Liam.Cryptography.Interfaces;
using Liam.Cryptography.Models;

namespace Liam.Cryptography.Services;

/// <summary>
/// RSA非对称加密服务实现
/// </summary>
public class RsaAsymmetricCrypto : IAsymmetricCrypto
{
    /// <summary>
    /// 生成密钥对
    /// </summary>
    /// <param name="keySize">密钥长度（位）</param>
    /// <returns>密钥对</returns>
    public KeyPair GenerateKeyPair(int keySize = 2048)
    {
        try
        {
            if (keySize < CryptoConstants.KeySizes.RSA_MINIMUM || keySize > CryptoConstants.KeySizes.RSA_MAXIMUM)
                throw new ArgumentException($"RSA密钥长度必须在{CryptoConstants.KeySizes.RSA_MINIMUM}到{CryptoConstants.KeySizes.RSA_MAXIMUM}位之间", nameof(keySize));

            using var rsa = RSA.Create(keySize);

            // 导出为PKCS#8格式的PEM（标准格式）
            var privateKeyPem = rsa.ExportPkcs8PrivateKeyPem();
            var publicKeyPem = rsa.ExportSubjectPublicKeyInfoPem();

            return new KeyPair(publicKeyPem, privateKeyPem, keySize, CryptoConstants.Algorithms.RSA);
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new KeyManagementException(CryptoConstants.ErrorMessages.KEY_GENERATION_FAILED, ex);
        }
    }

    /// <summary>
    /// 使用公钥加密数据
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>加密后的数据</returns>
    public byte[] EncryptWithPublicKey(string plainText, string publicKey)
    {
        try
        {
            if (plainText == null)
                throw new ArgumentNullException(nameof(plainText), "明文不能为null");
            // 允许空字符串作为有效输入

            if (string.IsNullOrEmpty(publicKey))
                throw new ArgumentException("公钥不能为空", nameof(publicKey));

            using var rsa = RSA.Create();
            // 支持PEM格式和Base64格式
            if (publicKey.StartsWith("-----BEGIN"))
            {
                rsa.ImportFromPem(publicKey);
            }
            else
            {
                rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);
            }

            var data = Encoding.UTF8.GetBytes(plainText);
            return rsa.Encrypt(data, RSAEncryptionPadding.OaepSHA256);
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new EncryptionException(CryptoConstants.ErrorMessages.ENCRYPTION_FAILED, ex);
        }
    }

    /// <summary>
    /// 使用公钥加密数据（异步）
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="publicKey">公钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加密后的数据</returns>
    public async Task<byte[]> EncryptWithPublicKeyAsync(string plainText, string publicKey, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => EncryptWithPublicKey(plainText, publicKey), cancellationToken);
    }

    /// <summary>
    /// 使用私钥解密数据
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>解密后的明文</returns>
    public string DecryptWithPrivateKey(byte[] cipherData, string privateKey)
    {
        try
        {
            if (cipherData == null)
                throw new ArgumentNullException(nameof(cipherData), "密文数据不能为null");
            if (cipherData.Length == 0)
                throw new ArgumentException("密文数据不能为空", nameof(cipherData));

            if (string.IsNullOrEmpty(privateKey))
                throw new ArgumentException("私钥不能为空", nameof(privateKey));

            using var rsa = RSA.Create();
            // 支持PEM格式和Base64格式
            if (privateKey.StartsWith("-----BEGIN"))
            {
                rsa.ImportFromPem(privateKey);
            }
            else
            {
                rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);
            }

            var decryptedData = rsa.Decrypt(cipherData, RSAEncryptionPadding.OaepSHA256);
            return Encoding.UTF8.GetString(decryptedData);
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new DecryptionException(CryptoConstants.ErrorMessages.DECRYPTION_FAILED, ex);
        }
    }

    /// <summary>
    /// 使用私钥解密数据（异步）
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="privateKey">私钥</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解密后的明文</returns>
    public async Task<string> DecryptWithPrivateKeyAsync(byte[] cipherData, string privateKey, CancellationToken cancellationToken = default)
    {
        return await Task.Run(() => DecryptWithPrivateKey(cipherData, privateKey), cancellationToken);
    }

    /// <summary>
    /// 使用私钥加密数据（用于数字签名）
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <param name="privateKey">私钥</param>
    /// <returns>加密后的数据</returns>
    public byte[] EncryptWithPrivateKey(string plainText, string privateKey)
    {
        try
        {
            if (plainText == null)
                throw new ArgumentNullException(nameof(plainText), "明文不能为null");
            // 允许空字符串作为有效输入

            if (string.IsNullOrEmpty(privateKey))
                throw new ArgumentException("私钥不能为空", nameof(privateKey));

            using var rsa = RSA.Create();
            // 支持PEM格式和Base64格式
            if (privateKey.StartsWith("-----BEGIN"))
            {
                rsa.ImportFromPem(privateKey);
            }
            else
            {
                rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);
            }

            var data = Encoding.UTF8.GetBytes(plainText);
            return rsa.SignData(data, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
        }
        catch (Exception ex) when (!(ex is ArgumentException))
        {
            throw new EncryptionException(CryptoConstants.ErrorMessages.ENCRYPTION_FAILED, ex);
        }
    }

    /// <summary>
    /// 使用公钥解密数据（用于验证数字签名）
    /// 注意：这个方法实际上不应该被直接使用，RSA公钥不能解密用私钥加密的数据
    /// 这里为了兼容测试，返回一个固定的错误消息
    /// </summary>
    /// <param name="cipherData">密文数据</param>
    /// <param name="publicKey">公钥</param>
    /// <returns>解密后的明文</returns>
    public string DecryptWithPublicKey(byte[] cipherData, string publicKey)
    {
        // RSA公钥不能解密用私钥加密的数据
        // 这个方法主要是为了API完整性，实际应该使用数字签名验证
        throw new NotSupportedException("RSA公钥不能解密数据，请使用数字签名验证功能");
    }
}
