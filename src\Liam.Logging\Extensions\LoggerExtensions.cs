using Liam.Logging.Constants;
using Liam.Logging.Interfaces;
using System.Runtime.CompilerServices;

namespace Liam.Logging.Extensions;

/// <summary>
/// 日志记录器扩展方法
/// </summary>
public static class LoggerExtensions
{
    /// <summary>
    /// 记录结构化跟踪日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="messageTemplate">消息模板</param>
    /// <param name="parameters">参数</param>
    public static void LogTraceStructured(this ILiamLogger logger, string messageTemplate, params object[] parameters)
    {
        logger.LogStructured(LogLevel.Trace, messageTemplate, parameters);
    }

    /// <summary>
    /// 记录结构化调试日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="messageTemplate">消息模板</param>
    /// <param name="parameters">参数</param>
    public static void LogDebugStructured(this ILiamLogger logger, string messageTemplate, params object[] parameters)
    {
        logger.LogStructured(LogLevel.Debug, messageTemplate, parameters);
    }

    /// <summary>
    /// 记录结构化信息日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="messageTemplate">消息模板</param>
    /// <param name="parameters">参数</param>
    public static void LogInformationStructured(this ILiamLogger logger, string messageTemplate, params object[] parameters)
    {
        logger.LogStructured(LogLevel.Information, messageTemplate, parameters);
    }

    /// <summary>
    /// 记录结构化警告日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="messageTemplate">消息模板</param>
    /// <param name="parameters">参数</param>
    public static void LogWarningStructured(this ILiamLogger logger, string messageTemplate, params object[] parameters)
    {
        logger.LogStructured(LogLevel.Warning, messageTemplate, parameters);
    }

    /// <summary>
    /// 记录结构化错误日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="messageTemplate">消息模板</param>
    /// <param name="parameters">参数</param>
    public static void LogErrorStructured(this ILiamLogger logger, string messageTemplate, params object[] parameters)
    {
        logger.LogStructured(LogLevel.Error, messageTemplate, parameters);
    }

    /// <summary>
    /// 记录结构化严重错误日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="messageTemplate">消息模板</param>
    /// <param name="parameters">参数</param>
    public static void LogCriticalStructured(this ILiamLogger logger, string messageTemplate, params object[] parameters)
    {
        logger.LogStructured(LogLevel.Critical, messageTemplate, parameters);
    }

    /// <summary>
    /// 异步记录跟踪日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="message">日志消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    /// <returns>异步任务</returns>
    public static Task LogTraceAsync(this ILiamLogger logger, string message,
        CancellationToken cancellationToken = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        return logger.LogAsync(LogLevel.Trace, message, null, cancellationToken, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 异步记录调试日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="message">日志消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    /// <returns>异步任务</returns>
    public static Task LogDebugAsync(this ILiamLogger logger, string message,
        CancellationToken cancellationToken = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        return logger.LogAsync(LogLevel.Debug, message, null, cancellationToken, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 异步记录信息日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="message">日志消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    /// <returns>异步任务</returns>
    public static Task LogInformationAsync(this ILiamLogger logger, string message,
        CancellationToken cancellationToken = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        return logger.LogAsync(LogLevel.Information, message, null, cancellationToken, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 异步记录警告日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    /// <returns>异步任务</returns>
    public static Task LogWarningAsync(this ILiamLogger logger, string message, Exception? exception = null,
        CancellationToken cancellationToken = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        return logger.LogAsync(LogLevel.Warning, message, exception, cancellationToken, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 异步记录错误日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    /// <returns>异步任务</returns>
    public static Task LogErrorAsync(this ILiamLogger logger, string message, Exception? exception = null,
        CancellationToken cancellationToken = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        return logger.LogAsync(LogLevel.Error, message, exception, cancellationToken, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 异步记录严重错误日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="message">日志消息</param>
    /// <param name="exception">异常信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    /// <returns>异步任务</returns>
    public static Task LogCriticalAsync(this ILiamLogger logger, string message, Exception? exception = null,
        CancellationToken cancellationToken = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        return logger.LogAsync(LogLevel.Critical, message, exception, cancellationToken, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// 记录方法进入日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="parameters">方法参数</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    public static void LogMethodEntry(this ILiamLogger logger, object? parameters = null,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        if (logger.IsEnabled(LogLevel.Trace))
        {
            var message = parameters != null 
                ? $"Entering method {memberName} with parameters: {parameters}"
                : $"Entering method {memberName}";
            
            logger.LogTrace(message, memberName, sourceFilePath, sourceLineNumber);
        }
    }

    /// <summary>
    /// 记录方法退出日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="result">返回值</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    public static void LogMethodExit(this ILiamLogger logger, object? result = null,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        if (logger.IsEnabled(LogLevel.Trace))
        {
            var message = result != null 
                ? $"Exiting method {memberName} with result: {result}"
                : $"Exiting method {memberName}";
            
            logger.LogTrace(message, memberName, sourceFilePath, sourceLineNumber);
        }
    }

    /// <summary>
    /// 记录性能日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="operationName">操作名称</param>
    /// <param name="duration">耗时</param>
    /// <param name="memberName">调用成员名称</param>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="sourceLineNumber">源代码行号</param>
    public static void LogPerformance(this ILiamLogger logger, string operationName, TimeSpan duration,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0)
    {
        if (logger.IsEnabled(LogLevel.Information))
        {
            logger.LogInformationStructured(
                "Performance: {OperationName} completed in {Duration}ms",
                operationName, duration.TotalMilliseconds);
        }
    }

    /// <summary>
    /// 使用作用域记录日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="scopeState">作用域状态</param>
    /// <param name="action">要执行的操作</param>
    public static void WithScope<TState>(this ILiamLogger logger, TState scopeState, Action action) where TState : notnull
    {
        using (logger.BeginScope(scopeState))
        {
            action();
        }
    }

    /// <summary>
    /// 异步使用作用域记录日志
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="scopeState">作用域状态</param>
    /// <param name="asyncAction">要执行的异步操作</param>
    /// <returns>异步任务</returns>
    public static async Task WithScopeAsync<TState>(this ILiamLogger logger, TState scopeState, Func<Task> asyncAction) where TState : notnull
    {
        using (logger.BeginScope(scopeState))
        {
            await asyncAction();
        }
    }

    /// <summary>
    /// 使用作用域记录日志（带返回值）
    /// </summary>
    /// <typeparam name="TState">作用域状态类型</typeparam>
    /// <typeparam name="TResult">返回值类型</typeparam>
    /// <param name="logger">日志记录器</param>
    /// <param name="scopeState">作用域状态</param>
    /// <param name="func">要执行的函数</param>
    /// <returns>函数返回值</returns>
    public static TResult WithScope<TState, TResult>(this ILiamLogger logger, TState scopeState, Func<TResult> func) where TState : notnull
    {
        using (logger.BeginScope(scopeState))
        {
            return func();
        }
    }

    /// <summary>
    /// 异步使用作用域记录日志（带返回值）
    /// </summary>
    /// <typeparam name="TState">作用域状态类型</typeparam>
    /// <typeparam name="TResult">返回值类型</typeparam>
    /// <param name="logger">日志记录器</param>
    /// <param name="scopeState">作用域状态</param>
    /// <param name="asyncFunc">要执行的异步函数</param>
    /// <returns>异步任务</returns>
    public static async Task<TResult> WithScopeAsync<TState, TResult>(this ILiamLogger logger, TState scopeState, Func<Task<TResult>> asyncFunc) where TState : notnull
    {
        using (logger.BeginScope(scopeState))
        {
            return await asyncFunc();
        }
    }
}
