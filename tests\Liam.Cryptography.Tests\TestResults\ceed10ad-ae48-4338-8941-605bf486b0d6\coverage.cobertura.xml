﻿<?xml version="1.0" encoding="utf-8"?>
<coverage line-rate="0.3914" branch-rate="0.48979999999999996" version="1.9" timestamp="1749953408" lines-covered="460" lines-valid="1175" branches-covered="145" branches-valid="296">
  <sources>
    <source>D:\Project\00 Liam\src\Liam.Cryptography\</source>
  </sources>
  <packages>
    <package name="Liam.Cryptography" line-rate="0.3914" branch-rate="0.48979999999999996" complexity="441">
      <classes>
        <class name="Liam.Cryptography.Services.AesSymmetricCrypto" filename="Services\AesSymmetricCrypto.cs" line-rate="0.528" branch-rate="0.48" complexity="51">
          <methods>
            <method name="Encrypt" signature="(System.String,System.Byte[],System.Byte[])" line-rate="0.9354" branch-rate="0.8332999999999999" complexity="12">
              <lines>
                <line number="25" hits="31" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="26" hits="1" branch="False" />
                <line number="29" hits="30" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="20" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="30" hits="1" branch="False" />
                <line number="31" hits="29" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="40" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="32" hits="0" branch="False" />
                <line number="34" hits="29" branch="False" />
                <line number="35" hits="29" branch="False" />
                <line number="36" hits="28" branch="False" />
                <line number="37" hits="28" branch="False" />
                <line number="39" hits="28" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="86" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="41" hits="18" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="93" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="42" hits="0" branch="False" />
                <line number="43" hits="18" branch="False" />
                <line number="47" hits="10" branch="False" />
                <line number="50" hits="28" branch="False" />
                <line number="51" hits="28" branch="False" />
                <line number="52" hits="28" branch="False" />
                <line number="53" hits="28" branch="False" />
                <line number="55" hits="28" branch="False" />
                <line number="56" hits="28" branch="False" />
                <line number="58" hits="28" branch="False" />
                <line number="61" hits="28" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="225" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="63" hits="10" branch="False" />
                <line number="64" hits="10" branch="False" />
                <line number="65" hits="10" branch="False" />
                <line number="66" hits="10" branch="False" />
                <line number="71" hits="18" branch="False" />
                <line number="74" hits="3" branch="False" />
                <line number="76" hits="1" branch="False" />
                <line number="78" hits="28" branch="False" />
              </lines>
            </method>
            <method name="Decrypt" signature="(System.Byte[],System.Byte[],System.Byte[])" line-rate="0.8125" branch-rate="0.5714" complexity="14">
              <lines>
                <line number="104" hits="24" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="105" hits="0" branch="False" />
                <line number="106" hits="24" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="21" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="107" hits="0" branch="False" />
                <line number="109" hits="24" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="40" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="110" hits="0" branch="False" />
                <line number="111" hits="24" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="60" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="112" hits="0" branch="False" />
                <line number="114" hits="24" branch="False" />
                <line number="115" hits="24" branch="False" />
                <line number="116" hits="24" branch="False" />
                <line number="117" hits="24" branch="False" />
                <line number="122" hits="24" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="106" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="125" hits="17" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="113" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="126" hits="0" branch="False" />
                <line number="127" hits="17" branch="False" />
                <line number="128" hits="17" branch="False" />
                <line number="133" hits="7" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="187" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="134" hits="0" branch="False" />
                <line number="136" hits="7" branch="False" />
                <line number="137" hits="7" branch="False" />
                <line number="139" hits="7" branch="False" />
                <line number="140" hits="7" branch="False" />
                <line number="143" hits="24" branch="False" />
                <line number="145" hits="24" branch="False" />
                <line number="146" hits="24" branch="False" />
                <line number="147" hits="24" branch="False" />
                <line number="148" hits="24" branch="False" />
                <line number="150" hits="24" branch="False" />
                <line number="152" hits="2" branch="False" />
                <line number="154" hits="2" branch="False" />
                <line number="156" hits="22" branch="False" />
              </lines>
            </method>
            <method name="GenerateKey" signature="(System.Int32)" line-rate="1" branch-rate="1" complexity="6">
              <lines>
                <line number="178" hits="13" branch="True" condition-coverage="100% (6/6)">
                  <conditions>
                    <condition number="6" type="jump" coverage="100%" />
                    <condition number="14" type="jump" coverage="100%" />
                    <condition number="22" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="179" hits="4" branch="False" />
                <line number="181" hits="9" branch="False" />
                <line number="182" hits="9" branch="False" />
                <line number="183" hits="9" branch="False" />
                <line number="184" hits="9" branch="False" />
                <line number="185" hits="9" branch="False" />
              </lines>
            </method>
            <method name="GenerateIV" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="193" hits="7" branch="False" />
                <line number="194" hits="7" branch="False" />
                <line number="195" hits="7" branch="False" />
                <line number="196" hits="7" branch="False" />
              </lines>
            </method>
            <method name="EncryptStream" signature="(System.IO.Stream,System.IO.Stream,System.Byte[],System.Byte[],System.Int32)" line-rate="0" branch-rate="0" complexity="8">
              <lines>
                <line number="208" hits="0" branch="False" />
                <line number="209" hits="0" branch="False" />
                <line number="210" hits="0" branch="False" />
                <line number="212" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="36" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="213" hits="0" branch="False" />
                <line number="217" hits="0" branch="False" />
                <line number="218" hits="0" branch="False" />
                <line number="219" hits="0" branch="False" />
                <line number="220" hits="0" branch="False" />
                <line number="222" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="84" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="224" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="92" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="225" hits="0" branch="False" />
                <line number="226" hits="0" branch="False" />
                <line number="230" hits="0" branch="False" />
                <line number="232" hits="0" branch="False" />
                <line number="235" hits="0" branch="False" />
                <line number="236" hits="0" branch="False" />
                <line number="238" hits="0" branch="False" />
                <line number="240" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="244" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="242" hits="0" branch="False" />
                <line number="244" hits="0" branch="False" />
                <line number="245" hits="0" branch="False" />
                <line number="247" hits="0" branch="False" />
                <line number="249" hits="0" branch="False" />
              </lines>
            </method>
            <method name="DecryptStream" signature="(System.IO.Stream,System.IO.Stream,System.Byte[],System.Byte[],System.Int32)" line-rate="0" branch-rate="0" complexity="10">
              <lines>
                <line number="315" hits="0" branch="False" />
                <line number="316" hits="0" branch="False" />
                <line number="317" hits="0" branch="False" />
                <line number="319" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="36" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="320" hits="0" branch="False" />
                <line number="324" hits="0" branch="False" />
                <line number="325" hits="0" branch="False" />
                <line number="326" hits="0" branch="False" />
                <line number="327" hits="0" branch="False" />
                <line number="330" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="84" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="332" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="92" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="333" hits="0" branch="False" />
                <line number="334" hits="0" branch="False" />
                <line number="339" hits="0" branch="False" />
                <line number="340" hits="0" branch="False" />
                <line number="341" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="182" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="342" hits="0" branch="False" />
                <line number="345" hits="0" branch="False" />
                <line number="347" hits="0" branch="False" />
                <line number="348" hits="0" branch="False" />
                <line number="350" hits="0" branch="False" />
                <line number="352" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="263" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="354" hits="0" branch="False" />
                <line number="356" hits="0" branch="False" />
                <line number="357" hits="0" branch="False" />
                <line number="359" hits="0" branch="False" />
                <line number="361" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="25" hits="31" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="26" hits="1" branch="False" />
            <line number="29" hits="30" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="20" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="30" hits="1" branch="False" />
            <line number="31" hits="29" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="40" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="32" hits="0" branch="False" />
            <line number="34" hits="29" branch="False" />
            <line number="35" hits="29" branch="False" />
            <line number="36" hits="28" branch="False" />
            <line number="37" hits="28" branch="False" />
            <line number="39" hits="28" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="86" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="41" hits="18" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="93" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="42" hits="0" branch="False" />
            <line number="43" hits="18" branch="False" />
            <line number="47" hits="10" branch="False" />
            <line number="50" hits="28" branch="False" />
            <line number="51" hits="28" branch="False" />
            <line number="52" hits="28" branch="False" />
            <line number="53" hits="28" branch="False" />
            <line number="55" hits="28" branch="False" />
            <line number="56" hits="28" branch="False" />
            <line number="58" hits="28" branch="False" />
            <line number="61" hits="28" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="225" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="63" hits="10" branch="False" />
            <line number="64" hits="10" branch="False" />
            <line number="65" hits="10" branch="False" />
            <line number="66" hits="10" branch="False" />
            <line number="71" hits="18" branch="False" />
            <line number="74" hits="3" branch="False" />
            <line number="76" hits="1" branch="False" />
            <line number="78" hits="28" branch="False" />
            <line number="104" hits="24" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="1" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="105" hits="0" branch="False" />
            <line number="106" hits="24" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="21" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="107" hits="0" branch="False" />
            <line number="109" hits="24" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="40" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="110" hits="0" branch="False" />
            <line number="111" hits="24" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="60" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="112" hits="0" branch="False" />
            <line number="114" hits="24" branch="False" />
            <line number="115" hits="24" branch="False" />
            <line number="116" hits="24" branch="False" />
            <line number="117" hits="24" branch="False" />
            <line number="122" hits="24" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="106" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="125" hits="17" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="113" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="126" hits="0" branch="False" />
            <line number="127" hits="17" branch="False" />
            <line number="128" hits="17" branch="False" />
            <line number="133" hits="7" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="187" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="134" hits="0" branch="False" />
            <line number="136" hits="7" branch="False" />
            <line number="137" hits="7" branch="False" />
            <line number="139" hits="7" branch="False" />
            <line number="140" hits="7" branch="False" />
            <line number="143" hits="24" branch="False" />
            <line number="145" hits="24" branch="False" />
            <line number="146" hits="24" branch="False" />
            <line number="147" hits="24" branch="False" />
            <line number="148" hits="24" branch="False" />
            <line number="150" hits="24" branch="False" />
            <line number="152" hits="2" branch="False" />
            <line number="154" hits="2" branch="False" />
            <line number="156" hits="22" branch="False" />
            <line number="178" hits="13" branch="True" condition-coverage="100% (6/6)">
              <conditions>
                <condition number="6" type="jump" coverage="100%" />
                <condition number="14" type="jump" coverage="100%" />
                <condition number="22" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="179" hits="4" branch="False" />
            <line number="181" hits="9" branch="False" />
            <line number="182" hits="9" branch="False" />
            <line number="183" hits="9" branch="False" />
            <line number="184" hits="9" branch="False" />
            <line number="185" hits="9" branch="False" />
            <line number="193" hits="7" branch="False" />
            <line number="194" hits="7" branch="False" />
            <line number="195" hits="7" branch="False" />
            <line number="196" hits="7" branch="False" />
            <line number="208" hits="0" branch="False" />
            <line number="209" hits="0" branch="False" />
            <line number="210" hits="0" branch="False" />
            <line number="212" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="36" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="213" hits="0" branch="False" />
            <line number="217" hits="0" branch="False" />
            <line number="218" hits="0" branch="False" />
            <line number="219" hits="0" branch="False" />
            <line number="220" hits="0" branch="False" />
            <line number="222" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="84" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="224" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="92" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="225" hits="0" branch="False" />
            <line number="226" hits="0" branch="False" />
            <line number="230" hits="0" branch="False" />
            <line number="232" hits="0" branch="False" />
            <line number="235" hits="0" branch="False" />
            <line number="236" hits="0" branch="False" />
            <line number="238" hits="0" branch="False" />
            <line number="240" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="244" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="242" hits="0" branch="False" />
            <line number="244" hits="0" branch="False" />
            <line number="245" hits="0" branch="False" />
            <line number="247" hits="0" branch="False" />
            <line number="249" hits="0" branch="False" />
            <line number="315" hits="0" branch="False" />
            <line number="316" hits="0" branch="False" />
            <line number="317" hits="0" branch="False" />
            <line number="319" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="36" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="320" hits="0" branch="False" />
            <line number="324" hits="0" branch="False" />
            <line number="325" hits="0" branch="False" />
            <line number="326" hits="0" branch="False" />
            <line number="327" hits="0" branch="False" />
            <line number="330" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="84" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="332" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="92" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="333" hits="0" branch="False" />
            <line number="334" hits="0" branch="False" />
            <line number="339" hits="0" branch="False" />
            <line number="340" hits="0" branch="False" />
            <line number="341" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="182" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="342" hits="0" branch="False" />
            <line number="345" hits="0" branch="False" />
            <line number="347" hits="0" branch="False" />
            <line number="348" hits="0" branch="False" />
            <line number="350" hits="0" branch="False" />
            <line number="352" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="263" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="354" hits="0" branch="False" />
            <line number="356" hits="0" branch="False" />
            <line number="357" hits="0" branch="False" />
            <line number="359" hits="0" branch="False" />
            <line number="361" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.AesSymmetricCrypto/&lt;&gt;c__DisplayClass1_0" filename="Services\AesSymmetricCrypto.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;EncryptAsync&gt;b__0" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="90" hits="6" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="90" hits="6" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.AesSymmetricCrypto/&lt;&gt;c__DisplayClass3_0" filename="Services\AesSymmetricCrypto.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;DecryptAsync&gt;b__0" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="168" hits="4" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="168" hits="4" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.AesSymmetricCrypto/&lt;DecryptAsync&gt;d__3" filename="Services\AesSymmetricCrypto.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="169" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="169" hits="2" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.AesSymmetricCrypto/&lt;DecryptStreamAsync&gt;d__9" filename="Services\AesSymmetricCrypto.cs" line-rate="0" branch-rate="0" complexity="14">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="14">
              <lines>
                <line number="374" hits="0" branch="False" />
                <line number="375" hits="0" branch="False" />
                <line number="376" hits="0" branch="False" />
                <line number="378" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="66" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="379" hits="0" branch="False" />
                <line number="383" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="111" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="384" hits="0" branch="False" />
                <line number="385" hits="0" branch="False" />
                <line number="386" hits="0" branch="False" />
                <line number="389" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="163" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="391" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="175" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="392" hits="0" branch="False" />
                <line number="393" hits="0" branch="False" />
                <line number="398" hits="0" branch="False" />
                <line number="399" hits="0" branch="False" />
                <line number="400" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="392" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="401" hits="0" branch="False" />
                <line number="404" hits="0" branch="False" />
                <line number="406" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="449" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="407" hits="0" branch="False" />
                <line number="409" hits="0" branch="False" />
                <line number="411" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="740" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="413" hits="0" branch="False" />
                <line number="415" hits="0" branch="False" />
                <line number="416" hits="0" branch="False" />
                <line number="418" hits="0" branch="False" />
                <line number="420" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="374" hits="0" branch="False" />
            <line number="375" hits="0" branch="False" />
            <line number="376" hits="0" branch="False" />
            <line number="378" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="66" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="379" hits="0" branch="False" />
            <line number="383" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="111" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="384" hits="0" branch="False" />
            <line number="385" hits="0" branch="False" />
            <line number="386" hits="0" branch="False" />
            <line number="389" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="163" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="391" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="175" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="392" hits="0" branch="False" />
            <line number="393" hits="0" branch="False" />
            <line number="398" hits="0" branch="False" />
            <line number="399" hits="0" branch="False" />
            <line number="400" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="392" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="401" hits="0" branch="False" />
            <line number="404" hits="0" branch="False" />
            <line number="406" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="449" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="407" hits="0" branch="False" />
            <line number="409" hits="0" branch="False" />
            <line number="411" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="740" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="413" hits="0" branch="False" />
            <line number="415" hits="0" branch="False" />
            <line number="416" hits="0" branch="False" />
            <line number="418" hits="0" branch="False" />
            <line number="420" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.AesSymmetricCrypto/&lt;EncryptAsync&gt;d__1" filename="Services\AesSymmetricCrypto.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="91" hits="3" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="91" hits="3" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.AesSymmetricCrypto/&lt;EncryptStreamAsync&gt;d__7" filename="Services\AesSymmetricCrypto.cs" line-rate="0" branch-rate="0" complexity="12">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="12">
              <lines>
                <line number="262" hits="0" branch="False" />
                <line number="263" hits="0" branch="False" />
                <line number="264" hits="0" branch="False" />
                <line number="266" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="66" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="267" hits="0" branch="False" />
                <line number="271" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="111" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="272" hits="0" branch="False" />
                <line number="273" hits="0" branch="False" />
                <line number="274" hits="0" branch="False" />
                <line number="276" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="163" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="278" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="175" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="279" hits="0" branch="False" />
                <line number="280" hits="0" branch="False" />
                <line number="284" hits="0" branch="False" />
                <line number="286" hits="0" branch="False" />
                <line number="289" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="419" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="290" hits="0" branch="False" />
                <line number="292" hits="0" branch="False" />
                <line number="294" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="710" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="296" hits="0" branch="False" />
                <line number="298" hits="0" branch="False" />
                <line number="299" hits="0" branch="False" />
                <line number="301" hits="0" branch="False" />
                <line number="303" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="262" hits="0" branch="False" />
            <line number="263" hits="0" branch="False" />
            <line number="264" hits="0" branch="False" />
            <line number="266" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="66" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="267" hits="0" branch="False" />
            <line number="271" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="111" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="272" hits="0" branch="False" />
            <line number="273" hits="0" branch="False" />
            <line number="274" hits="0" branch="False" />
            <line number="276" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="163" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="278" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="175" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="279" hits="0" branch="False" />
            <line number="280" hits="0" branch="False" />
            <line number="284" hits="0" branch="False" />
            <line number="286" hits="0" branch="False" />
            <line number="289" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="419" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="290" hits="0" branch="False" />
            <line number="292" hits="0" branch="False" />
            <line number="294" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="710" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="296" hits="0" branch="False" />
            <line number="298" hits="0" branch="False" />
            <line number="299" hits="0" branch="False" />
            <line number="301" hits="0" branch="False" />
            <line number="303" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Argon2PasswordHasher" filename="Services\Argon2PasswordHasher.cs" line-rate="0" branch-rate="0" complexity="13">
          <methods>
            <method name="HashPassword" signature="(System.String,Liam.Cryptography.Services.Argon2PasswordHasher/Argon2Options)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="99" hits="0" branch="False" />
                <line number="101" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="13" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="102" hits="0" branch="False" />
                <line number="103" hits="0" branch="False" />
                <line number="105" hits="0" branch="False" />
                <line number="107" hits="0" branch="False" />
                <line number="109" hits="0" branch="False" />
                <line number="111" hits="0" branch="False" />
              </lines>
            </method>
            <method name="HashPassword" signature="(System.String,System.Byte[],Liam.Cryptography.Services.Argon2PasswordHasher/Argon2Options)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="124" hits="0" branch="False" />
                <line number="125" hits="0" branch="False" />
                <line number="127" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="24" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="128" hits="0" branch="False" />
                <line number="130" hits="0" branch="False" />
                <line number="131" hits="0" branch="False" />
                <line number="132" hits="0" branch="False" />
                <line number="133" hits="0" branch="False" />
                <line number="134" hits="0" branch="False" />
                <line number="136" hits="0" branch="False" />
                <line number="138" hits="0" branch="False" />
                <line number="139" hits="0" branch="False" />
                <line number="140" hits="0" branch="False" />
                <line number="141" hits="0" branch="False" />
                <line number="142" hits="0" branch="False" />
                <line number="143" hits="0" branch="False" />
                <line number="145" hits="0" branch="False" />
                <line number="147" hits="0" branch="False" />
                <line number="149" hits="0" branch="False" />
              </lines>
            </method>
            <method name="VerifyPassword" signature="(System.String,Liam.Cryptography.Services.Argon2PasswordHasher/Argon2HashResult)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="225" hits="0" branch="False" />
                <line number="226" hits="0" branch="False" />
                <line number="228" hits="0" branch="False" />
                <line number="229" hits="0" branch="False" />
                <line number="231" hits="0" branch="False" />
                <line number="233" hits="0" branch="False" />
                <line number="235" hits="0" branch="False" />
                <line number="237" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ParseFormattedHash" signature="(System.String)" line-rate="0" branch-rate="0" complexity="6">
              <lines>
                <line number="273" hits="0" branch="False" />
                <line number="275" hits="0" branch="False" />
                <line number="276" hits="0" branch="True" condition-coverage="0% (0/6)">
                  <conditions>
                    <condition number="25" type="jump" coverage="0%" />
                    <condition number="40" type="jump" coverage="0%" />
                    <condition number="55" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="277" hits="0" branch="False" />
                <line number="279" hits="0" branch="False" />
                <line number="280" hits="0" branch="False" />
                <line number="281" hits="0" branch="False" />
                <line number="282" hits="0" branch="False" />
                <line number="284" hits="0" branch="False" />
                <line number="285" hits="0" branch="False" />
                <line number="287" hits="0" branch="False" />
                <line number="288" hits="0" branch="False" />
                <line number="289" hits="0" branch="False" />
                <line number="290" hits="0" branch="False" />
                <line number="291" hits="0" branch="False" />
                <line number="292" hits="0" branch="False" />
                <line number="293" hits="0" branch="False" />
                <line number="294" hits="0" branch="False" />
                <line number="295" hits="0" branch="False" />
                <line number="296" hits="0" branch="False" />
                <line number="297" hits="0" branch="False" />
                <line number="299" hits="0" branch="False" />
                <line number="301" hits="0" branch="False" />
                <line number="303" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(Liam.Cryptography.Services.Argon2PasswordHasher/Argon2Options)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="84" hits="0" branch="False" />
                <line number="86" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="87" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="99" hits="0" branch="False" />
            <line number="101" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="13" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="102" hits="0" branch="False" />
            <line number="103" hits="0" branch="False" />
            <line number="105" hits="0" branch="False" />
            <line number="107" hits="0" branch="False" />
            <line number="109" hits="0" branch="False" />
            <line number="111" hits="0" branch="False" />
            <line number="124" hits="0" branch="False" />
            <line number="125" hits="0" branch="False" />
            <line number="127" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="24" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="128" hits="0" branch="False" />
            <line number="130" hits="0" branch="False" />
            <line number="131" hits="0" branch="False" />
            <line number="132" hits="0" branch="False" />
            <line number="133" hits="0" branch="False" />
            <line number="134" hits="0" branch="False" />
            <line number="136" hits="0" branch="False" />
            <line number="138" hits="0" branch="False" />
            <line number="139" hits="0" branch="False" />
            <line number="140" hits="0" branch="False" />
            <line number="141" hits="0" branch="False" />
            <line number="142" hits="0" branch="False" />
            <line number="143" hits="0" branch="False" />
            <line number="145" hits="0" branch="False" />
            <line number="147" hits="0" branch="False" />
            <line number="149" hits="0" branch="False" />
            <line number="225" hits="0" branch="False" />
            <line number="226" hits="0" branch="False" />
            <line number="228" hits="0" branch="False" />
            <line number="229" hits="0" branch="False" />
            <line number="231" hits="0" branch="False" />
            <line number="233" hits="0" branch="False" />
            <line number="235" hits="0" branch="False" />
            <line number="237" hits="0" branch="False" />
            <line number="273" hits="0" branch="False" />
            <line number="275" hits="0" branch="False" />
            <line number="276" hits="0" branch="True" condition-coverage="0% (0/6)">
              <conditions>
                <condition number="25" type="jump" coverage="0%" />
                <condition number="40" type="jump" coverage="0%" />
                <condition number="55" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="277" hits="0" branch="False" />
            <line number="279" hits="0" branch="False" />
            <line number="280" hits="0" branch="False" />
            <line number="281" hits="0" branch="False" />
            <line number="282" hits="0" branch="False" />
            <line number="284" hits="0" branch="False" />
            <line number="285" hits="0" branch="False" />
            <line number="287" hits="0" branch="False" />
            <line number="288" hits="0" branch="False" />
            <line number="289" hits="0" branch="False" />
            <line number="290" hits="0" branch="False" />
            <line number="291" hits="0" branch="False" />
            <line number="292" hits="0" branch="False" />
            <line number="293" hits="0" branch="False" />
            <line number="294" hits="0" branch="False" />
            <line number="295" hits="0" branch="False" />
            <line number="296" hits="0" branch="False" />
            <line number="297" hits="0" branch="False" />
            <line number="299" hits="0" branch="False" />
            <line number="301" hits="0" branch="False" />
            <line number="303" hits="0" branch="False" />
            <line number="84" hits="0" branch="False" />
            <line number="86" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="87" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Argon2PasswordHasher/Argon2Options" filename="Services\Argon2PasswordHasher.cs" line-rate="0" branch-rate="1" complexity="5">
          <methods>
            <method name="get_SaltSize" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="28" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_HashSize" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="33" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Iterations" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="38" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_MemorySize" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="43" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_DegreeOfParallelism" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="48" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="28" hits="0" branch="False" />
            <line number="33" hits="0" branch="False" />
            <line number="38" hits="0" branch="False" />
            <line number="43" hits="0" branch="False" />
            <line number="48" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Argon2PasswordHasher/Argon2HashResult" filename="Services\Argon2PasswordHasher.cs" line-rate="0" branch-rate="1" complexity="4">
          <methods>
            <method name="get_Hash" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="59" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Salt" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="64" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Options" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="69" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_FormattedHash" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="75" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="59" hits="0" branch="False" />
            <line number="64" hits="0" branch="False" />
            <line number="69" hits="0" branch="False" />
            <line number="75" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Argon2PasswordHasher/&lt;HashPasswordAsync&gt;d__11" filename="Services\Argon2PasswordHasher.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="162" hits="0" branch="False" />
                <line number="164" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="46" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="165" hits="0" branch="False" />
                <line number="166" hits="0" branch="False" />
                <line number="168" hits="0" branch="False" />
                <line number="170" hits="0" branch="False" />
                <line number="172" hits="0" branch="False" />
                <line number="174" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="162" hits="0" branch="False" />
            <line number="164" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="46" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="165" hits="0" branch="False" />
            <line number="166" hits="0" branch="False" />
            <line number="168" hits="0" branch="False" />
            <line number="170" hits="0" branch="False" />
            <line number="172" hits="0" branch="False" />
            <line number="174" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Argon2PasswordHasher/&lt;HashPasswordAsync&gt;d__12" filename="Services\Argon2PasswordHasher.cs" line-rate="0" branch-rate="0" complexity="2">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="188" hits="0" branch="False" />
                <line number="189" hits="0" branch="False" />
                <line number="191" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="60" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="192" hits="0" branch="False" />
                <line number="194" hits="0" branch="False" />
                <line number="195" hits="0" branch="False" />
                <line number="196" hits="0" branch="False" />
                <line number="197" hits="0" branch="False" />
                <line number="198" hits="0" branch="False" />
                <line number="200" hits="0" branch="False" />
                <line number="202" hits="0" branch="False" />
                <line number="203" hits="0" branch="False" />
                <line number="204" hits="0" branch="False" />
                <line number="205" hits="0" branch="False" />
                <line number="206" hits="0" branch="False" />
                <line number="207" hits="0" branch="False" />
                <line number="209" hits="0" branch="False" />
                <line number="211" hits="0" branch="False" />
                <line number="213" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="188" hits="0" branch="False" />
            <line number="189" hits="0" branch="False" />
            <line number="191" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="60" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="192" hits="0" branch="False" />
            <line number="194" hits="0" branch="False" />
            <line number="195" hits="0" branch="False" />
            <line number="196" hits="0" branch="False" />
            <line number="197" hits="0" branch="False" />
            <line number="198" hits="0" branch="False" />
            <line number="200" hits="0" branch="False" />
            <line number="202" hits="0" branch="False" />
            <line number="203" hits="0" branch="False" />
            <line number="204" hits="0" branch="False" />
            <line number="205" hits="0" branch="False" />
            <line number="206" hits="0" branch="False" />
            <line number="207" hits="0" branch="False" />
            <line number="209" hits="0" branch="False" />
            <line number="211" hits="0" branch="False" />
            <line number="213" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Argon2PasswordHasher/&lt;VerifyPasswordAsync&gt;d__14" filename="Services\Argon2PasswordHasher.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="250" hits="0" branch="False" />
                <line number="251" hits="0" branch="False" />
                <line number="253" hits="0" branch="False" />
                <line number="254" hits="0" branch="False" />
                <line number="256" hits="0" branch="False" />
                <line number="258" hits="0" branch="False" />
                <line number="260" hits="0" branch="False" />
                <line number="262" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="250" hits="0" branch="False" />
            <line number="251" hits="0" branch="False" />
            <line number="253" hits="0" branch="False" />
            <line number="254" hits="0" branch="False" />
            <line number="256" hits="0" branch="False" />
            <line number="258" hits="0" branch="False" />
            <line number="260" hits="0" branch="False" />
            <line number="262" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.ChaCha20Poly1305Crypto" filename="Services\ChaCha20Poly1305Crypto.cs" line-rate="0" branch-rate="0" complexity="21">
          <methods>
            <method name="Encrypt" signature="(System.String,System.Byte[],System.Byte[])" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="28" hits="0" branch="False" />
                <line number="29" hits="0" branch="False" />
                <line number="31" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="27" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="32" hits="0" branch="False" />
                <line number="34" hits="0" branch="False" />
                <line number="35" hits="0" branch="False" />
                <line number="37" hits="0" branch="False" />
                <line number="39" hits="0" branch="False" />
                <line number="41" hits="0" branch="False" />
              </lines>
            </method>
            <method name="EncryptBytes" signature="(System.Byte[],System.Byte[],System.Byte[])" line-rate="0" branch-rate="0" complexity="6">
              <lines>
                <line number="54" hits="0" branch="False" />
                <line number="55" hits="0" branch="False" />
                <line number="57" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="27" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="58" hits="0" branch="False" />
                <line number="61" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="91" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="63" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="98" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="64" hits="0" branch="False" />
                <line number="65" hits="0" branch="False" />
                <line number="69" hits="0" branch="False" />
                <line number="70" hits="0" branch="False" />
                <line number="73" hits="0" branch="False" />
                <line number="74" hits="0" branch="False" />
                <line number="75" hits="0" branch="False" />
                <line number="77" hits="0" branch="False" />
                <line number="80" hits="0" branch="False" />
                <line number="81" hits="0" branch="False" />
                <line number="82" hits="0" branch="False" />
                <line number="83" hits="0" branch="False" />
                <line number="85" hits="0" branch="False" />
                <line number="87" hits="0" branch="False" />
                <line number="89" hits="0" branch="False" />
                <line number="91" hits="0" branch="False" />
              </lines>
            </method>
            <method name="Decrypt" signature="(System.Byte[],System.Byte[],System.Byte[])" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="117" hits="0" branch="False" />
                <line number="118" hits="0" branch="False" />
                <line number="120" hits="0" branch="False" />
                <line number="122" hits="0" branch="False" />
                <line number="124" hits="0" branch="False" />
              </lines>
            </method>
            <method name="DecryptBytes" signature="(System.Byte[],System.Byte[],System.Byte[])" line-rate="0" branch-rate="0" complexity="8">
              <lines>
                <line number="137" hits="0" branch="False" />
                <line number="138" hits="0" branch="False" />
                <line number="140" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="27" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="141" hits="0" branch="False" />
                <line number="143" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="95" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="144" hits="0" branch="False" />
                <line number="150" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="114" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="152" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="121" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="153" hits="0" branch="False" />
                <line number="154" hits="0" branch="False" />
                <line number="156" hits="0" branch="False" />
                <line number="157" hits="0" branch="False" />
                <line number="158" hits="0" branch="False" />
                <line number="159" hits="0" branch="False" />
                <line number="164" hits="0" branch="False" />
                <line number="165" hits="0" branch="False" />
                <line number="166" hits="0" branch="False" />
                <line number="168" hits="0" branch="False" />
                <line number="169" hits="0" branch="False" />
                <line number="170" hits="0" branch="False" />
                <line number="173" hits="0" branch="False" />
                <line number="174" hits="0" branch="False" />
                <line number="176" hits="0" branch="False" />
                <line number="178" hits="0" branch="False" />
                <line number="180" hits="0" branch="False" />
                <line number="182" hits="0" branch="False" />
                <line number="184" hits="0" branch="False" />
              </lines>
            </method>
            <method name="GenerateKey" signature="(System.Int32)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="206" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="6" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="207" hits="0" branch="False" />
                <line number="209" hits="0" branch="False" />
                <line number="210" hits="0" branch="False" />
                <line number="211" hits="0" branch="False" />
              </lines>
            </method>
            <method name="GenerateNonce" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="220" hits="0" branch="False" />
                <line number="221" hits="0" branch="False" />
                <line number="222" hits="0" branch="False" />
              </lines>
            </method>
            <method name="GenerateIV" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="231" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="28" hits="0" branch="False" />
            <line number="29" hits="0" branch="False" />
            <line number="31" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="27" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="32" hits="0" branch="False" />
            <line number="34" hits="0" branch="False" />
            <line number="35" hits="0" branch="False" />
            <line number="37" hits="0" branch="False" />
            <line number="39" hits="0" branch="False" />
            <line number="41" hits="0" branch="False" />
            <line number="54" hits="0" branch="False" />
            <line number="55" hits="0" branch="False" />
            <line number="57" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="27" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="58" hits="0" branch="False" />
            <line number="61" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="91" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="63" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="98" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="64" hits="0" branch="False" />
            <line number="65" hits="0" branch="False" />
            <line number="69" hits="0" branch="False" />
            <line number="70" hits="0" branch="False" />
            <line number="73" hits="0" branch="False" />
            <line number="74" hits="0" branch="False" />
            <line number="75" hits="0" branch="False" />
            <line number="77" hits="0" branch="False" />
            <line number="80" hits="0" branch="False" />
            <line number="81" hits="0" branch="False" />
            <line number="82" hits="0" branch="False" />
            <line number="83" hits="0" branch="False" />
            <line number="85" hits="0" branch="False" />
            <line number="87" hits="0" branch="False" />
            <line number="89" hits="0" branch="False" />
            <line number="91" hits="0" branch="False" />
            <line number="117" hits="0" branch="False" />
            <line number="118" hits="0" branch="False" />
            <line number="120" hits="0" branch="False" />
            <line number="122" hits="0" branch="False" />
            <line number="124" hits="0" branch="False" />
            <line number="137" hits="0" branch="False" />
            <line number="138" hits="0" branch="False" />
            <line number="140" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="27" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="141" hits="0" branch="False" />
            <line number="143" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="95" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="144" hits="0" branch="False" />
            <line number="150" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="114" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="152" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="121" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="153" hits="0" branch="False" />
            <line number="154" hits="0" branch="False" />
            <line number="156" hits="0" branch="False" />
            <line number="157" hits="0" branch="False" />
            <line number="158" hits="0" branch="False" />
            <line number="159" hits="0" branch="False" />
            <line number="164" hits="0" branch="False" />
            <line number="165" hits="0" branch="False" />
            <line number="166" hits="0" branch="False" />
            <line number="168" hits="0" branch="False" />
            <line number="169" hits="0" branch="False" />
            <line number="170" hits="0" branch="False" />
            <line number="173" hits="0" branch="False" />
            <line number="174" hits="0" branch="False" />
            <line number="176" hits="0" branch="False" />
            <line number="178" hits="0" branch="False" />
            <line number="180" hits="0" branch="False" />
            <line number="182" hits="0" branch="False" />
            <line number="184" hits="0" branch="False" />
            <line number="206" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="6" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="207" hits="0" branch="False" />
            <line number="209" hits="0" branch="False" />
            <line number="210" hits="0" branch="False" />
            <line number="211" hits="0" branch="False" />
            <line number="220" hits="0" branch="False" />
            <line number="221" hits="0" branch="False" />
            <line number="222" hits="0" branch="False" />
            <line number="231" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.ChaCha20Poly1305Crypto/&lt;&gt;c__DisplayClass5_0" filename="Services\ChaCha20Poly1305Crypto.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;EncryptAsync&gt;b__0" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="103" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="103" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.ChaCha20Poly1305Crypto/&lt;&gt;c__DisplayClass8_0" filename="Services\ChaCha20Poly1305Crypto.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;DecryptAsync&gt;b__0" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="196" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="196" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.ChaCha20Poly1305Crypto/&lt;DecryptAsync&gt;d__8" filename="Services\ChaCha20Poly1305Crypto.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="197" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="197" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.ChaCha20Poly1305Crypto/&lt;EncryptAsync&gt;d__5" filename="Services\ChaCha20Poly1305Crypto.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="104" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="104" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.CryptoKeyManager" filename="Services\CryptoKeyManager.cs" line-rate="0.8896" branch-rate="0.7585999999999999" complexity="61">
          <methods>
            <method name="GenerateSymmetricKey" signature="(System.Int32)" line-rate="0.8887999999999999" branch-rate="1" complexity="6">
              <lines>
                <line number="31" hits="18" branch="True" condition-coverage="100% (6/6)">
                  <conditions>
                    <condition number="6" type="jump" coverage="100%" />
                    <condition number="14" type="jump" coverage="100%" />
                    <condition number="22" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="32" hits="4" branch="False" />
                <line number="34" hits="14" branch="False" />
                <line number="35" hits="14" branch="False" />
                <line number="36" hits="14" branch="False" />
                <line number="37" hits="14" branch="False" />
                <line number="39" hits="4" branch="False" />
                <line number="41" hits="0" branch="False" />
                <line number="43" hits="14" branch="False" />
              </lines>
            </method>
            <method name="GenerateAsymmetricKeyPair" signature="(System.Int32)" line-rate="0.8887999999999999" branch-rate="1" complexity="4">
              <lines>
                <line number="54" hits="16" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="6" type="jump" coverage="100%" />
                    <condition number="14" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="55" hits="4" branch="False" />
                <line number="57" hits="12" branch="False" />
                <line number="59" hits="12" branch="False" />
                <line number="60" hits="12" branch="False" />
                <line number="62" hits="12" branch="False" />
                <line number="64" hits="4" branch="False" />
                <line number="66" hits="0" branch="False" />
                <line number="68" hits="12" branch="False" />
              </lines>
            </method>
            <method name="ExportKey" signature="(System.Byte[],System.String,System.String)" line-rate="0.8234999999999999" branch-rate="0.8332999999999999" complexity="12">
              <lines>
                <line number="80" hits="15" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="81" hits="1" branch="False" />
                <line number="82" hits="14" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="21" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="83" hits="0" branch="False" />
                <line number="85" hits="14" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="45" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="86" hits="1" branch="False" />
                <line number="88" hits="13" branch="False" />
                <line number="89" hits="13" branch="True" condition-coverage="75% (3/4)">
                  <conditions>
                    <condition number="76" type="jump" coverage="100%" />
                    <condition number="84" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="91" hits="0" branch="False" />
                <line number="94" hits="13" branch="False" />
                <line number="96" hits="13" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="106" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="99" hits="4" branch="False" />
                <line number="102" hits="13" branch="False" />
                <line number="103" hits="13" branch="False" />
                <line number="104" hits="2" branch="False" />
                <line number="106" hits="0" branch="False" />
                <line number="108" hits="13" branch="False" />
              </lines>
            </method>
            <method name="ExportKeyPair" signature="(Liam.Cryptography.Models.KeyPair,System.String,System.String,System.String)" line-rate="0.7692" branch-rate="0.625" complexity="8">
              <lines>
                <line number="119" hits="4" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="120" hits="1" branch="False" />
                <line number="121" hits="3" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="25" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="122" hits="0" branch="False" />
                <line number="126" hits="3" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="59" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="129" hits="0" branch="False" />
                <line number="134" hits="3" branch="False" />
                <line number="135" hits="3" branch="False" />
                <line number="139" hits="3" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="113" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="142" hits="0" branch="False" />
                <line number="147" hits="3" branch="False" />
                <line number="148" hits="3" branch="False" />
                <line number="150" hits="3" branch="False" />
              </lines>
            </method>
            <method name="ImportKey" signature="(System.String,System.String)" line-rate="0.9333" branch-rate="0.8332999999999999" complexity="6">
              <lines>
                <line number="162" hits="16" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="6" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="163" hits="0" branch="False" />
                <line number="165" hits="16" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="30" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="166" hits="1" branch="False" />
                <line number="168" hits="15" branch="False" />
                <line number="170" hits="15" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="57" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="175" hits="4" branch="False" />
                <line number="176" hits="2" branch="False" />
                <line number="177" hits="2" branch="False" />
                <line number="180" hits="2" branch="False" />
                <line number="181" hits="2" branch="False" />
                <line number="184" hits="15" branch="False" />
                <line number="186" hits="6" branch="False" />
                <line number="188" hits="5" branch="False" />
                <line number="190" hits="10" branch="False" />
              </lines>
            </method>
            <method name="ImportPrivateKey" signature="(System.String,System.String)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="200" hits="4" branch="False" />
                <line number="201" hits="2" branch="False" />
              </lines>
            </method>
            <method name="ImportPublicKey" signature="(System.String)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="211" hits="3" branch="False" />
                <line number="212" hits="2" branch="False" />
              </lines>
            </method>
            <method name="ValidateKey" signature="(System.String,Liam.Cryptography.Models.KeyType)" line-rate="0.8332999999999999" branch-rate="0.6666" complexity="12">
              <lines>
                <line number="225" hits="6" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="6" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="226" hits="2" branch="True" condition-coverage="75% (3/4)">
                  <conditions>
                    <condition number="16" type="switch" coverage="75%" />
                  </conditions>
                </line>
                <line number="231" hits="1" branch="False" />
                <line number="233" hits="1" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="55" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="235" hits="0" branch="False" />
                <line number="239" hits="1" branch="False" />
                <line number="241" hits="1" branch="False" />
                <line number="245" hits="1" branch="False" />
                <line number="247" hits="1" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="122" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="249" hits="0" branch="False" />
                <line number="253" hits="1" branch="False" />
                <line number="255" hits="1" branch="False" />
                <line number="259" hits="2" branch="False" />
                <line number="260" hits="1" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="184" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="263" hits="0" branch="False" />
                <line number="266" hits="1" branch="False" />
                <line number="268" hits="1" branch="False" />
                <line number="270" hits="6" branch="False" />
              </lines>
            </method>
            <method name="ExtractPublicKey" signature="(System.String)" line-rate="0.75" branch-rate="0.5" complexity="4">
              <lines>
                <line number="281" hits="2" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="6" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="282" hits="0" branch="False" />
                <line number="284" hits="2" branch="False" />
                <line number="287" hits="2" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="41" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="289" hits="0" branch="False" />
                <line number="290" hits="0" branch="False" />
                <line number="294" hits="2" branch="False" />
                <line number="295" hits="1" branch="False" />
                <line number="296" hits="1" branch="False" />
                <line number="299" hits="1" branch="False" />
                <line number="301" hits="1" branch="False" />
                <line number="303" hits="1" branch="False" />
              </lines>
            </method>
            <method name="EncryptKeyWithPassword" signature="(System.String,System.String)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="314" hits="4" branch="False" />
                <line number="315" hits="4" branch="False" />
                <line number="317" hits="4" branch="False" />
                <line number="318" hits="4" branch="False" />
                <line number="321" hits="4" branch="False" />
                <line number="322" hits="4" branch="False" />
                <line number="325" hits="4" branch="False" />
                <line number="326" hits="4" branch="False" />
                <line number="328" hits="4" branch="False" />
                <line number="329" hits="4" branch="False" />
                <line number="332" hits="4" branch="False" />
                <line number="333" hits="4" branch="False" />
                <line number="334" hits="4" branch="False" />
                <line number="336" hits="4" branch="False" />
                <line number="337" hits="4" branch="False" />
                <line number="340" hits="4" branch="False" />
                <line number="341" hits="4" branch="False" />
                <line number="343" hits="4" branch="False" />
                <line number="344" hits="4" branch="False" />
                <line number="345" hits="4" branch="False" />
                <line number="346" hits="4" branch="False" />
                <line number="347" hits="4" branch="False" />
                <line number="348" hits="4" branch="False" />
                <line number="349" hits="4" branch="False" />
                <line number="350" hits="4" branch="False" />
                <line number="352" hits="4" branch="False" />
                <line number="353" hits="4" branch="False" />
              </lines>
            </method>
            <method name="DecryptKeyWithPassword" signature="(System.String,System.String)" line-rate="0.92" branch-rate="0.5" complexity="4">
              <lines>
                <line number="363" hits="4" branch="False" />
                <line number="366" hits="4" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="11" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="367" hits="0" branch="False" />
                <line number="369" hits="4" branch="False" />
                <line number="372" hits="4" branch="False" />
                <line number="373" hits="4" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="34" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="374" hits="0" branch="False" />
                <line number="377" hits="4" branch="False" />
                <line number="378" hits="4" branch="False" />
                <line number="379" hits="4" branch="False" />
                <line number="382" hits="4" branch="False" />
                <line number="383" hits="4" branch="False" />
                <line number="384" hits="4" branch="False" />
                <line number="387" hits="4" branch="False" />
                <line number="388" hits="4" branch="False" />
                <line number="389" hits="4" branch="False" />
                <line number="392" hits="4" branch="False" />
                <line number="393" hits="4" branch="False" />
                <line number="396" hits="4" branch="False" />
                <line number="397" hits="4" branch="False" />
                <line number="400" hits="4" branch="False" />
                <line number="401" hits="4" branch="False" />
                <line number="402" hits="4" branch="False" />
                <line number="404" hits="2" branch="False" />
                <line number="405" hits="2" branch="False" />
              </lines>
            </method>
            <method name="DecryptKeyWithPasswordLegacy" signature="(System.String,System.String)" line-rate="1" branch-rate="1" complexity="2">
              <lines>
                <line number="415" hits="2" branch="False" />
                <line number="416" hits="2" branch="False" />
                <line number="418" hits="3512" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="50" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="420" hits="1754" branch="False" />
                <line number="423" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="31" hits="18" branch="True" condition-coverage="100% (6/6)">
              <conditions>
                <condition number="6" type="jump" coverage="100%" />
                <condition number="14" type="jump" coverage="100%" />
                <condition number="22" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="32" hits="4" branch="False" />
            <line number="34" hits="14" branch="False" />
            <line number="35" hits="14" branch="False" />
            <line number="36" hits="14" branch="False" />
            <line number="37" hits="14" branch="False" />
            <line number="39" hits="4" branch="False" />
            <line number="41" hits="0" branch="False" />
            <line number="43" hits="14" branch="False" />
            <line number="54" hits="16" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="6" type="jump" coverage="100%" />
                <condition number="14" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="55" hits="4" branch="False" />
            <line number="57" hits="12" branch="False" />
            <line number="59" hits="12" branch="False" />
            <line number="60" hits="12" branch="False" />
            <line number="62" hits="12" branch="False" />
            <line number="64" hits="4" branch="False" />
            <line number="66" hits="0" branch="False" />
            <line number="68" hits="12" branch="False" />
            <line number="80" hits="15" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="81" hits="1" branch="False" />
            <line number="82" hits="14" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="21" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="83" hits="0" branch="False" />
            <line number="85" hits="14" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="45" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="86" hits="1" branch="False" />
            <line number="88" hits="13" branch="False" />
            <line number="89" hits="13" branch="True" condition-coverage="75% (3/4)">
              <conditions>
                <condition number="76" type="jump" coverage="100%" />
                <condition number="84" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="91" hits="0" branch="False" />
            <line number="94" hits="13" branch="False" />
            <line number="96" hits="13" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="106" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="99" hits="4" branch="False" />
            <line number="102" hits="13" branch="False" />
            <line number="103" hits="13" branch="False" />
            <line number="104" hits="2" branch="False" />
            <line number="106" hits="0" branch="False" />
            <line number="108" hits="13" branch="False" />
            <line number="119" hits="4" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="120" hits="1" branch="False" />
            <line number="121" hits="3" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="25" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="122" hits="0" branch="False" />
            <line number="126" hits="3" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="59" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="129" hits="0" branch="False" />
            <line number="134" hits="3" branch="False" />
            <line number="135" hits="3" branch="False" />
            <line number="139" hits="3" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="113" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="142" hits="0" branch="False" />
            <line number="147" hits="3" branch="False" />
            <line number="148" hits="3" branch="False" />
            <line number="150" hits="3" branch="False" />
            <line number="162" hits="16" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="6" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="163" hits="0" branch="False" />
            <line number="165" hits="16" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="30" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="166" hits="1" branch="False" />
            <line number="168" hits="15" branch="False" />
            <line number="170" hits="15" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="57" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="175" hits="4" branch="False" />
            <line number="176" hits="2" branch="False" />
            <line number="177" hits="2" branch="False" />
            <line number="180" hits="2" branch="False" />
            <line number="181" hits="2" branch="False" />
            <line number="184" hits="15" branch="False" />
            <line number="186" hits="6" branch="False" />
            <line number="188" hits="5" branch="False" />
            <line number="190" hits="10" branch="False" />
            <line number="200" hits="4" branch="False" />
            <line number="201" hits="2" branch="False" />
            <line number="211" hits="3" branch="False" />
            <line number="212" hits="2" branch="False" />
            <line number="225" hits="6" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="6" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="226" hits="2" branch="True" condition-coverage="75% (3/4)">
              <conditions>
                <condition number="16" type="switch" coverage="75%" />
              </conditions>
            </line>
            <line number="231" hits="1" branch="False" />
            <line number="233" hits="1" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="55" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="235" hits="0" branch="False" />
            <line number="239" hits="1" branch="False" />
            <line number="241" hits="1" branch="False" />
            <line number="245" hits="1" branch="False" />
            <line number="247" hits="1" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="122" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="249" hits="0" branch="False" />
            <line number="253" hits="1" branch="False" />
            <line number="255" hits="1" branch="False" />
            <line number="259" hits="2" branch="False" />
            <line number="260" hits="1" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="184" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="263" hits="0" branch="False" />
            <line number="266" hits="1" branch="False" />
            <line number="268" hits="1" branch="False" />
            <line number="270" hits="6" branch="False" />
            <line number="281" hits="2" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="6" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="282" hits="0" branch="False" />
            <line number="284" hits="2" branch="False" />
            <line number="287" hits="2" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="41" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="289" hits="0" branch="False" />
            <line number="290" hits="0" branch="False" />
            <line number="294" hits="2" branch="False" />
            <line number="295" hits="1" branch="False" />
            <line number="296" hits="1" branch="False" />
            <line number="299" hits="1" branch="False" />
            <line number="301" hits="1" branch="False" />
            <line number="303" hits="1" branch="False" />
            <line number="314" hits="4" branch="False" />
            <line number="315" hits="4" branch="False" />
            <line number="317" hits="4" branch="False" />
            <line number="318" hits="4" branch="False" />
            <line number="321" hits="4" branch="False" />
            <line number="322" hits="4" branch="False" />
            <line number="325" hits="4" branch="False" />
            <line number="326" hits="4" branch="False" />
            <line number="328" hits="4" branch="False" />
            <line number="329" hits="4" branch="False" />
            <line number="332" hits="4" branch="False" />
            <line number="333" hits="4" branch="False" />
            <line number="334" hits="4" branch="False" />
            <line number="336" hits="4" branch="False" />
            <line number="337" hits="4" branch="False" />
            <line number="340" hits="4" branch="False" />
            <line number="341" hits="4" branch="False" />
            <line number="343" hits="4" branch="False" />
            <line number="344" hits="4" branch="False" />
            <line number="345" hits="4" branch="False" />
            <line number="346" hits="4" branch="False" />
            <line number="347" hits="4" branch="False" />
            <line number="348" hits="4" branch="False" />
            <line number="349" hits="4" branch="False" />
            <line number="350" hits="4" branch="False" />
            <line number="352" hits="4" branch="False" />
            <line number="353" hits="4" branch="False" />
            <line number="363" hits="4" branch="False" />
            <line number="366" hits="4" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="11" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="367" hits="0" branch="False" />
            <line number="369" hits="4" branch="False" />
            <line number="372" hits="4" branch="False" />
            <line number="373" hits="4" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="34" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="374" hits="0" branch="False" />
            <line number="377" hits="4" branch="False" />
            <line number="378" hits="4" branch="False" />
            <line number="379" hits="4" branch="False" />
            <line number="382" hits="4" branch="False" />
            <line number="383" hits="4" branch="False" />
            <line number="384" hits="4" branch="False" />
            <line number="387" hits="4" branch="False" />
            <line number="388" hits="4" branch="False" />
            <line number="389" hits="4" branch="False" />
            <line number="392" hits="4" branch="False" />
            <line number="393" hits="4" branch="False" />
            <line number="396" hits="4" branch="False" />
            <line number="397" hits="4" branch="False" />
            <line number="400" hits="4" branch="False" />
            <line number="401" hits="4" branch="False" />
            <line number="402" hits="4" branch="False" />
            <line number="404" hits="2" branch="False" />
            <line number="405" hits="2" branch="False" />
            <line number="415" hits="2" branch="False" />
            <line number="416" hits="2" branch="False" />
            <line number="418" hits="3512" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="50" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="420" hits="1754" branch="False" />
            <line number="423" hits="2" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Ed25519DigitalSignature" filename="Services\Ed25519DigitalSignature.cs" line-rate="0" branch-rate="1" complexity="7">
          <methods>
            <method name="GenerateKeyPair" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="24" hits="0" branch="False" />
                <line number="25" hits="0" branch="False" />
                <line number="26" hits="0" branch="False" />
                <line number="28" hits="0" branch="False" />
                <line number="29" hits="0" branch="False" />
                <line number="30" hits="0" branch="False" />
                <line number="31" hits="0" branch="False" />
                <line number="32" hits="0" branch="False" />
                <line number="34" hits="0" branch="False" />
                <line number="36" hits="0" branch="False" />
                <line number="38" hits="0" branch="False" />
              </lines>
            </method>
            <method name="Sign" signature="(System.String,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="50" hits="0" branch="False" />
                <line number="51" hits="0" branch="False" />
                <line number="53" hits="0" branch="False" />
                <line number="54" hits="0" branch="False" />
                <line number="56" hits="0" branch="False" />
                <line number="58" hits="0" branch="False" />
                <line number="60" hits="0" branch="False" />
              </lines>
            </method>
            <method name="Sign" signature="(System.Byte[],System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="72" hits="0" branch="False" />
                <line number="73" hits="0" branch="False" />
                <line number="75" hits="0" branch="False" />
                <line number="76" hits="0" branch="False" />
                <line number="77" hits="0" branch="False" />
                <line number="79" hits="0" branch="False" />
                <line number="81" hits="0" branch="False" />
                <line number="83" hits="0" branch="False" />
                <line number="85" hits="0" branch="False" />
              </lines>
            </method>
            <method name="Verify" signature="(System.String,System.Byte[],System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="122" hits="0" branch="False" />
                <line number="123" hits="0" branch="False" />
                <line number="124" hits="0" branch="False" />
                <line number="126" hits="0" branch="False" />
                <line number="127" hits="0" branch="False" />
                <line number="129" hits="0" branch="False" />
                <line number="132" hits="0" branch="False" />
                <line number="134" hits="0" branch="False" />
              </lines>
            </method>
            <method name="Verify" signature="(System.Byte[],System.Byte[],System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="147" hits="0" branch="False" />
                <line number="148" hits="0" branch="False" />
                <line number="149" hits="0" branch="False" />
                <line number="151" hits="0" branch="False" />
                <line number="152" hits="0" branch="False" />
                <line number="153" hits="0" branch="False" />
                <line number="155" hits="0" branch="False" />
                <line number="157" hits="0" branch="False" />
                <line number="160" hits="0" branch="False" />
                <line number="162" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ExtractPublicKey" signature="(System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="199" hits="0" branch="False" />
                <line number="201" hits="0" branch="False" />
                <line number="202" hits="0" branch="False" />
                <line number="203" hits="0" branch="False" />
                <line number="205" hits="0" branch="False" />
                <line number="206" hits="0" branch="False" />
                <line number="208" hits="0" branch="False" />
                <line number="210" hits="0" branch="False" />
                <line number="212" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ValidateKeyPair" signature="(System.String,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="224" hits="0" branch="False" />
                <line number="225" hits="0" branch="False" />
                <line number="227" hits="0" branch="False" />
                <line number="228" hits="0" branch="False" />
                <line number="230" hits="0" branch="False" />
                <line number="232" hits="0" branch="False" />
                <line number="234" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="24" hits="0" branch="False" />
            <line number="25" hits="0" branch="False" />
            <line number="26" hits="0" branch="False" />
            <line number="28" hits="0" branch="False" />
            <line number="29" hits="0" branch="False" />
            <line number="30" hits="0" branch="False" />
            <line number="31" hits="0" branch="False" />
            <line number="32" hits="0" branch="False" />
            <line number="34" hits="0" branch="False" />
            <line number="36" hits="0" branch="False" />
            <line number="38" hits="0" branch="False" />
            <line number="50" hits="0" branch="False" />
            <line number="51" hits="0" branch="False" />
            <line number="53" hits="0" branch="False" />
            <line number="54" hits="0" branch="False" />
            <line number="56" hits="0" branch="False" />
            <line number="58" hits="0" branch="False" />
            <line number="60" hits="0" branch="False" />
            <line number="72" hits="0" branch="False" />
            <line number="73" hits="0" branch="False" />
            <line number="75" hits="0" branch="False" />
            <line number="76" hits="0" branch="False" />
            <line number="77" hits="0" branch="False" />
            <line number="79" hits="0" branch="False" />
            <line number="81" hits="0" branch="False" />
            <line number="83" hits="0" branch="False" />
            <line number="85" hits="0" branch="False" />
            <line number="122" hits="0" branch="False" />
            <line number="123" hits="0" branch="False" />
            <line number="124" hits="0" branch="False" />
            <line number="126" hits="0" branch="False" />
            <line number="127" hits="0" branch="False" />
            <line number="129" hits="0" branch="False" />
            <line number="132" hits="0" branch="False" />
            <line number="134" hits="0" branch="False" />
            <line number="147" hits="0" branch="False" />
            <line number="148" hits="0" branch="False" />
            <line number="149" hits="0" branch="False" />
            <line number="151" hits="0" branch="False" />
            <line number="152" hits="0" branch="False" />
            <line number="153" hits="0" branch="False" />
            <line number="155" hits="0" branch="False" />
            <line number="157" hits="0" branch="False" />
            <line number="160" hits="0" branch="False" />
            <line number="162" hits="0" branch="False" />
            <line number="199" hits="0" branch="False" />
            <line number="201" hits="0" branch="False" />
            <line number="202" hits="0" branch="False" />
            <line number="203" hits="0" branch="False" />
            <line number="205" hits="0" branch="False" />
            <line number="206" hits="0" branch="False" />
            <line number="208" hits="0" branch="False" />
            <line number="210" hits="0" branch="False" />
            <line number="212" hits="0" branch="False" />
            <line number="224" hits="0" branch="False" />
            <line number="225" hits="0" branch="False" />
            <line number="227" hits="0" branch="False" />
            <line number="228" hits="0" branch="False" />
            <line number="230" hits="0" branch="False" />
            <line number="232" hits="0" branch="False" />
            <line number="234" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Ed25519DigitalSignature/&lt;&gt;c__DisplayClass3_0" filename="Services\Ed25519DigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;SignAsync&gt;b__0" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="96" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="96" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Ed25519DigitalSignature/&lt;&gt;c__DisplayClass4_0" filename="Services\Ed25519DigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;SignAsync&gt;b__0" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="108" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="108" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Ed25519DigitalSignature/&lt;&gt;c__DisplayClass7_0" filename="Services\Ed25519DigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;VerifyAsync&gt;b__0" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="174" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="174" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Ed25519DigitalSignature/&lt;&gt;c__DisplayClass8_0" filename="Services\Ed25519DigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;VerifyAsync&gt;b__0" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="187" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="187" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Ed25519DigitalSignature/&lt;SignAsync&gt;d__3" filename="Services\Ed25519DigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="97" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="97" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Ed25519DigitalSignature/&lt;SignAsync&gt;d__4" filename="Services\Ed25519DigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="109" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="109" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Ed25519DigitalSignature/&lt;VerifyAsync&gt;d__7" filename="Services\Ed25519DigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="175" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="175" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Ed25519DigitalSignature/&lt;VerifyAsync&gt;d__8" filename="Services\Ed25519DigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="188" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="188" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaAsymmetricCrypto" filename="Services\RsaAsymmetricCrypto.cs" line-rate="0.8" branch-rate="0.7082999999999999" complexity="24">
          <methods>
            <method name="GenerateKeyPair" signature="(System.Int32)" line-rate="0.8887999999999999" branch-rate="1" complexity="4">
              <lines>
                <line number="24" hits="30" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="6" type="jump" coverage="100%" />
                    <condition number="14" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="25" hits="4" branch="False" />
                <line number="27" hits="26" branch="False" />
                <line number="30" hits="26" branch="False" />
                <line number="31" hits="26" branch="False" />
                <line number="33" hits="26" branch="False" />
                <line number="35" hits="4" branch="False" />
                <line number="37" hits="0" branch="False" />
                <line number="39" hits="26" branch="False" />
              </lines>
            </method>
            <method name="EncryptWithPublicKey" signature="(System.String,System.String)" line-rate="1" branch-rate="1" complexity="6">
              <lines>
                <line number="51" hits="29" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="52" hits="1" branch="False" />
                <line number="55" hits="28" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="25" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="56" hits="1" branch="False" />
                <line number="58" hits="27" branch="False" />
                <line number="60" hits="27" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="60" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="62" hits="26" branch="False" />
                <line number="66" hits="1" branch="False" />
                <line number="69" hits="26" branch="False" />
                <line number="70" hits="26" branch="False" />
                <line number="72" hits="5" branch="False" />
                <line number="74" hits="3" branch="False" />
                <line number="76" hits="24" branch="False" />
              </lines>
            </method>
            <method name="DecryptWithPrivateKey" signature="(System.Byte[],System.String)" line-rate="0.7333" branch-rate="0.5" complexity="8">
              <lines>
                <line number="100" hits="22" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="101" hits="0" branch="False" />
                <line number="102" hits="22" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="21" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="103" hits="0" branch="False" />
                <line number="105" hits="22" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="45" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="106" hits="0" branch="False" />
                <line number="108" hits="22" branch="False" />
                <line number="110" hits="22" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="80" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="112" hits="22" branch="False" />
                <line number="116" hits="0" branch="False" />
                <line number="119" hits="22" branch="False" />
                <line number="120" hits="20" branch="False" />
                <line number="122" hits="2" branch="False" />
                <line number="124" hits="2" branch="False" />
                <line number="126" hits="20" branch="False" />
              </lines>
            </method>
            <method name="EncryptWithPrivateKey" signature="(System.String,System.String)" line-rate="0.6153" branch-rate="0.5" complexity="6">
              <lines>
                <line number="150" hits="2" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="151" hits="0" branch="False" />
                <line number="154" hits="2" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="25" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="155" hits="0" branch="False" />
                <line number="157" hits="2" branch="False" />
                <line number="159" hits="2" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="60" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="161" hits="2" branch="False" />
                <line number="165" hits="0" branch="False" />
                <line number="168" hits="2" branch="False" />
                <line number="169" hits="2" branch="False" />
                <line number="171" hits="0" branch="False" />
                <line number="173" hits="0" branch="False" />
                <line number="175" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="24" hits="30" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="6" type="jump" coverage="100%" />
                <condition number="14" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="25" hits="4" branch="False" />
            <line number="27" hits="26" branch="False" />
            <line number="30" hits="26" branch="False" />
            <line number="31" hits="26" branch="False" />
            <line number="33" hits="26" branch="False" />
            <line number="35" hits="4" branch="False" />
            <line number="37" hits="0" branch="False" />
            <line number="39" hits="26" branch="False" />
            <line number="51" hits="29" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="52" hits="1" branch="False" />
            <line number="55" hits="28" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="25" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="56" hits="1" branch="False" />
            <line number="58" hits="27" branch="False" />
            <line number="60" hits="27" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="60" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="62" hits="26" branch="False" />
            <line number="66" hits="1" branch="False" />
            <line number="69" hits="26" branch="False" />
            <line number="70" hits="26" branch="False" />
            <line number="72" hits="5" branch="False" />
            <line number="74" hits="3" branch="False" />
            <line number="76" hits="24" branch="False" />
            <line number="100" hits="22" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="1" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="101" hits="0" branch="False" />
            <line number="102" hits="22" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="21" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="103" hits="0" branch="False" />
            <line number="105" hits="22" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="45" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="106" hits="0" branch="False" />
            <line number="108" hits="22" branch="False" />
            <line number="110" hits="22" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="80" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="112" hits="22" branch="False" />
            <line number="116" hits="0" branch="False" />
            <line number="119" hits="22" branch="False" />
            <line number="120" hits="20" branch="False" />
            <line number="122" hits="2" branch="False" />
            <line number="124" hits="2" branch="False" />
            <line number="126" hits="20" branch="False" />
            <line number="150" hits="2" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="1" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="151" hits="0" branch="False" />
            <line number="154" hits="2" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="25" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="155" hits="0" branch="False" />
            <line number="157" hits="2" branch="False" />
            <line number="159" hits="2" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="60" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="161" hits="2" branch="False" />
            <line number="165" hits="0" branch="False" />
            <line number="168" hits="2" branch="False" />
            <line number="169" hits="2" branch="False" />
            <line number="171" hits="0" branch="False" />
            <line number="173" hits="0" branch="False" />
            <line number="175" hits="2" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaAsymmetricCrypto/&lt;&gt;c__DisplayClass2_0" filename="Services\RsaAsymmetricCrypto.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;EncryptWithPublicKeyAsync&gt;b__0" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="87" hits="6" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="87" hits="6" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaAsymmetricCrypto/&lt;&gt;c__DisplayClass4_0" filename="Services\RsaAsymmetricCrypto.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;DecryptWithPrivateKeyAsync&gt;b__0" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="137" hits="4" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="137" hits="4" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaAsymmetricCrypto/&lt;DecryptWithPrivateKeyAsync&gt;d__4" filename="Services\RsaAsymmetricCrypto.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="138" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="138" hits="2" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaAsymmetricCrypto/&lt;EncryptWithPublicKeyAsync&gt;d__2" filename="Services\RsaAsymmetricCrypto.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="88" hits="3" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="88" hits="3" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaDigitalSignature" filename="Services\RsaDigitalSignature.cs" line-rate="0.9166" branch-rate="0.85" complexity="20">
          <methods>
            <method name="Sign" signature="(System.String,System.String)" line-rate="1" branch-rate="1" complexity="2">
              <lines>
                <line number="22" hits="51" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="23" hits="1" branch="False" />
                <line number="26" hits="50" branch="False" />
                <line number="27" hits="50" branch="False" />
              </lines>
            </method>
            <method name="Sign" signature="(System.Byte[],System.String)" line-rate="0.9166" branch-rate="0.8332999999999999" complexity="6">
              <lines>
                <line number="40" hits="50" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="41" hits="0" branch="False" />
                <line number="44" hits="50" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="25" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="45" hits="1" branch="False" />
                <line number="47" hits="49" branch="False" />
                <line number="49" hits="49" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="60" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="51" hits="48" branch="False" />
                <line number="55" hits="1" branch="False" />
                <line number="58" hits="48" branch="False" />
                <line number="60" hits="2" branch="False" />
                <line number="62" hits="1" branch="False" />
                <line number="64" hits="48" branch="False" />
              </lines>
            </method>
            <method name="Verify" signature="(System.String,System.Byte[],System.String)" line-rate="1" branch-rate="1" complexity="2">
              <lines>
                <line number="99" hits="29" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="100" hits="1" branch="False" />
                <line number="103" hits="28" branch="False" />
                <line number="104" hits="28" branch="False" />
              </lines>
            </method>
            <method name="Verify" signature="(System.Byte[],System.Byte[],System.String)" line-rate="0.875" branch-rate="0.8" complexity="10">
              <lines>
                <line number="117" hits="28" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="118" hits="0" branch="False" />
                <line number="119" hits="28" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="20" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="120" hits="1" branch="False" />
                <line number="121" hits="27" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="40" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="122" hits="0" branch="False" />
                <line number="123" hits="27" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="64" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="124" hits="1" branch="False" />
                <line number="129" hits="26" branch="False" />
                <line number="131" hits="26" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="100" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="133" hits="25" branch="False" />
                <line number="137" hits="1" branch="False" />
                <line number="140" hits="25" branch="False" />
                <line number="142" hits="1" branch="False" />
                <line number="145" hits="1" branch="False" />
                <line number="147" hits="26" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="22" hits="51" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="23" hits="1" branch="False" />
            <line number="26" hits="50" branch="False" />
            <line number="27" hits="50" branch="False" />
            <line number="40" hits="50" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="1" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="41" hits="0" branch="False" />
            <line number="44" hits="50" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="25" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="45" hits="1" branch="False" />
            <line number="47" hits="49" branch="False" />
            <line number="49" hits="49" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="60" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="51" hits="48" branch="False" />
            <line number="55" hits="1" branch="False" />
            <line number="58" hits="48" branch="False" />
            <line number="60" hits="2" branch="False" />
            <line number="62" hits="1" branch="False" />
            <line number="64" hits="48" branch="False" />
            <line number="99" hits="29" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="100" hits="1" branch="False" />
            <line number="103" hits="28" branch="False" />
            <line number="104" hits="28" branch="False" />
            <line number="117" hits="28" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="1" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="118" hits="0" branch="False" />
            <line number="119" hits="28" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="20" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="120" hits="1" branch="False" />
            <line number="121" hits="27" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="40" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="122" hits="0" branch="False" />
            <line number="123" hits="27" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="64" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="124" hits="1" branch="False" />
            <line number="129" hits="26" branch="False" />
            <line number="131" hits="26" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="100" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="133" hits="25" branch="False" />
            <line number="137" hits="1" branch="False" />
            <line number="140" hits="25" branch="False" />
            <line number="142" hits="1" branch="False" />
            <line number="145" hits="1" branch="False" />
            <line number="147" hits="26" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaDigitalSignature/&lt;&gt;c__DisplayClass2_0" filename="Services\RsaDigitalSignature.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;SignAsync&gt;b__0" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="75" hits="8" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="75" hits="8" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaDigitalSignature/&lt;&gt;c__DisplayClass3_0" filename="Services\RsaDigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;SignAsync&gt;b__0" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="87" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="87" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaDigitalSignature/&lt;&gt;c__DisplayClass6_0" filename="Services\RsaDigitalSignature.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;VerifyAsync&gt;b__0" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="159" hits="4" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="159" hits="4" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaDigitalSignature/&lt;&gt;c__DisplayClass7_0" filename="Services\RsaDigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;VerifyAsync&gt;b__0" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="172" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="172" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaDigitalSignature/&lt;SignAsync&gt;d__2" filename="Services\RsaDigitalSignature.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="76" hits="4" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="76" hits="4" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaDigitalSignature/&lt;SignAsync&gt;d__3" filename="Services\RsaDigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="88" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="88" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaDigitalSignature/&lt;VerifyAsync&gt;d__6" filename="Services\RsaDigitalSignature.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="160" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="160" hits="2" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.RsaDigitalSignature/&lt;VerifyAsync&gt;d__7" filename="Services\RsaDigitalSignature.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="173" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="173" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Sha256HashProvider" filename="Services\Sha256HashProvider.cs" line-rate="0.5918" branch-rate="0.625" complexity="16">
          <methods>
            <method name="ComputeHash" signature="(System.String)" line-rate="1" branch-rate="1" complexity="2">
              <lines>
                <line number="21" hits="39" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="22" hits="1" branch="False" />
                <line number="25" hits="38" branch="False" />
                <line number="26" hits="38" branch="False" />
              </lines>
            </method>
            <method name="ComputeHash" signature="(System.Byte[])" line-rate="0.875" branch-rate="1" complexity="2">
              <lines>
                <line number="38" hits="58" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="39" hits="2" branch="False" />
                <line number="42" hits="56" branch="False" />
                <line number="43" hits="56" branch="False" />
                <line number="44" hits="56" branch="False" />
                <line number="46" hits="2" branch="False" />
                <line number="48" hits="0" branch="False" />
                <line number="50" hits="56" branch="False" />
              </lines>
            </method>
            <method name="ComputeFileHash" signature="(System.String)" line-rate="0.909" branch-rate="1" complexity="4">
              <lines>
                <line number="83" hits="6" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="6" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="84" hits="2" branch="False" />
                <line number="86" hits="4" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="30" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="87" hits="1" branch="False" />
                <line number="89" hits="3" branch="False" />
                <line number="90" hits="3" branch="False" />
                <line number="91" hits="3" branch="False" />
                <line number="92" hits="3" branch="False" />
                <line number="94" hits="3" branch="False" />
                <line number="96" hits="0" branch="False" />
                <line number="98" hits="3" branch="False" />
              </lines>
            </method>
            <method name="ComputeStreamHash" signature="(System.IO.Stream,System.Int32)" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="137" hits="0" branch="False" />
                <line number="139" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="13" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="140" hits="0" branch="False" />
                <line number="142" hits="0" branch="False" />
                <line number="143" hits="0" branch="False" />
                <line number="146" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="72" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="148" hits="0" branch="False" />
                <line number="151" hits="0" branch="False" />
                <line number="152" hits="0" branch="False" />
                <line number="154" hits="0" branch="False" />
                <line number="156" hits="0" branch="False" />
                <line number="158" hits="0" branch="False" />
              </lines>
            </method>
            <method name="VerifyHash" signature="(System.String,System.String)" line-rate="0.5714" branch-rate="0.5" complexity="2">
              <lines>
                <line number="204" hits="3" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="6" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="205" hits="0" branch="False" />
                <line number="207" hits="3" branch="False" />
                <line number="208" hits="3" branch="False" />
                <line number="210" hits="0" branch="False" />
                <line number="212" hits="0" branch="False" />
                <line number="214" hits="3" branch="False" />
              </lines>
            </method>
            <method name="VerifyHash" signature="(System.Byte[],System.String)" line-rate="0.5714" branch-rate="0.5" complexity="2">
              <lines>
                <line number="226" hits="1" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="6" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="227" hits="0" branch="False" />
                <line number="229" hits="1" branch="False" />
                <line number="230" hits="1" branch="False" />
                <line number="232" hits="0" branch="False" />
                <line number="234" hits="0" branch="False" />
                <line number="236" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="21" hits="39" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="22" hits="1" branch="False" />
            <line number="25" hits="38" branch="False" />
            <line number="26" hits="38" branch="False" />
            <line number="38" hits="58" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="39" hits="2" branch="False" />
            <line number="42" hits="56" branch="False" />
            <line number="43" hits="56" branch="False" />
            <line number="44" hits="56" branch="False" />
            <line number="46" hits="2" branch="False" />
            <line number="48" hits="0" branch="False" />
            <line number="50" hits="56" branch="False" />
            <line number="83" hits="6" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="6" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="84" hits="2" branch="False" />
            <line number="86" hits="4" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="30" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="87" hits="1" branch="False" />
            <line number="89" hits="3" branch="False" />
            <line number="90" hits="3" branch="False" />
            <line number="91" hits="3" branch="False" />
            <line number="92" hits="3" branch="False" />
            <line number="94" hits="3" branch="False" />
            <line number="96" hits="0" branch="False" />
            <line number="98" hits="3" branch="False" />
            <line number="137" hits="0" branch="False" />
            <line number="139" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="13" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="140" hits="0" branch="False" />
            <line number="142" hits="0" branch="False" />
            <line number="143" hits="0" branch="False" />
            <line number="146" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="72" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="148" hits="0" branch="False" />
            <line number="151" hits="0" branch="False" />
            <line number="152" hits="0" branch="False" />
            <line number="154" hits="0" branch="False" />
            <line number="156" hits="0" branch="False" />
            <line number="158" hits="0" branch="False" />
            <line number="204" hits="3" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="6" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="205" hits="0" branch="False" />
            <line number="207" hits="3" branch="False" />
            <line number="208" hits="3" branch="False" />
            <line number="210" hits="0" branch="False" />
            <line number="212" hits="0" branch="False" />
            <line number="214" hits="3" branch="False" />
            <line number="226" hits="1" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="6" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="227" hits="0" branch="False" />
            <line number="229" hits="1" branch="False" />
            <line number="230" hits="1" branch="False" />
            <line number="232" hits="0" branch="False" />
            <line number="234" hits="0" branch="False" />
            <line number="236" hits="1" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Sha256HashProvider/&lt;&gt;c__DisplayClass2_0" filename="Services\Sha256HashProvider.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;ComputeHashAsync&gt;b__0" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="60" hits="4" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="60" hits="4" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Sha256HashProvider/&lt;&gt;c__DisplayClass3_0" filename="Services\Sha256HashProvider.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="&lt;ComputeHashAsync&gt;b__0" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="71" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="71" hits="2" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Sha256HashProvider/&lt;ComputeFileHashAsync&gt;d__5" filename="Services\Sha256HashProvider.cs" line-rate="0.6363" branch-rate="0.5" complexity="4">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0.6363" branch-rate="0.5" complexity="4">
              <lines>
                <line number="110" hits="1" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="24" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="111" hits="0" branch="False" />
                <line number="113" hits="1" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="53" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="114" hits="0" branch="False" />
                <line number="116" hits="1" branch="False" />
                <line number="117" hits="1" branch="False" />
                <line number="118" hits="1" branch="False" />
                <line number="119" hits="1" branch="False" />
                <line number="121" hits="0" branch="False" />
                <line number="123" hits="0" branch="False" />
                <line number="125" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="110" hits="1" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="24" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="111" hits="0" branch="False" />
            <line number="113" hits="1" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="53" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="114" hits="0" branch="False" />
            <line number="116" hits="1" branch="False" />
            <line number="117" hits="1" branch="False" />
            <line number="118" hits="1" branch="False" />
            <line number="119" hits="1" branch="False" />
            <line number="121" hits="0" branch="False" />
            <line number="123" hits="0" branch="False" />
            <line number="125" hits="1" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Sha256HashProvider/&lt;ComputeHashAsync&gt;d__2" filename="Services\Sha256HashProvider.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="61" hits="2" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="61" hits="2" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Sha256HashProvider/&lt;ComputeHashAsync&gt;d__3" filename="Services\Sha256HashProvider.cs" line-rate="1" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="72" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="72" hits="1" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Services.Sha256HashProvider/&lt;ComputeStreamHashAsync&gt;d__7" filename="Services\Sha256HashProvider.cs" line-rate="0" branch-rate="0" complexity="4">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="171" hits="0" branch="False" />
                <line number="173" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="36" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="174" hits="0" branch="False" />
                <line number="176" hits="0" branch="False" />
                <line number="177" hits="0" branch="False" />
                <line number="180" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="230" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="182" hits="0" branch="False" />
                <line number="185" hits="0" branch="False" />
                <line number="186" hits="0" branch="False" />
                <line number="188" hits="0" branch="False" />
                <line number="190" hits="0" branch="False" />
                <line number="192" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="171" hits="0" branch="False" />
            <line number="173" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="36" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="174" hits="0" branch="False" />
            <line number="176" hits="0" branch="False" />
            <line number="177" hits="0" branch="False" />
            <line number="180" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="230" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="182" hits="0" branch="False" />
            <line number="185" hits="0" branch="False" />
            <line number="186" hits="0" branch="False" />
            <line number="188" hits="0" branch="False" />
            <line number="190" hits="0" branch="False" />
            <line number="192" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Models.EncryptionResult" filename="Models\EncryptionResult.cs" line-rate="0" branch-rate="0" complexity="14">
          <methods>
            <method name="get_Data" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="11" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_IV" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="16" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Salt" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="21" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_Algorithm" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="26" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_KeySize" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="31" hits="0" branch="False" />
              </lines>
            </method>
            <method name="get_EncryptedAt" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="36" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ToBase64" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="68" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ToHex" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="77" hits="0" branch="False" />
              </lines>
            </method>
            <method name="IsValid" signature="()" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="86" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="7" type="jump" coverage="0%" />
                    <condition number="20" type="jump" coverage="0%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name=".ctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="41" hits="0" branch="False" />
                <line number="43" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.Byte[],System.String,System.Int32,System.Byte[],System.Byte[])" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="53" hits="0" branch="False" />
                <line number="55" hits="0" branch="False" />
                <line number="56" hits="0" branch="False" />
                <line number="57" hits="0" branch="False" />
                <line number="58" hits="0" branch="False" />
                <line number="59" hits="0" branch="False" />
                <line number="60" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="11" hits="0" branch="False" />
            <line number="16" hits="0" branch="False" />
            <line number="21" hits="0" branch="False" />
            <line number="26" hits="0" branch="False" />
            <line number="31" hits="0" branch="False" />
            <line number="36" hits="0" branch="False" />
            <line number="68" hits="0" branch="False" />
            <line number="77" hits="0" branch="False" />
            <line number="86" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="7" type="jump" coverage="0%" />
                <condition number="20" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="41" hits="0" branch="False" />
            <line number="43" hits="0" branch="False" />
            <line number="53" hits="0" branch="False" />
            <line number="55" hits="0" branch="False" />
            <line number="56" hits="0" branch="False" />
            <line number="57" hits="0" branch="False" />
            <line number="58" hits="0" branch="False" />
            <line number="59" hits="0" branch="False" />
            <line number="60" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Models.KeyPair" filename="Models\KeyPair.cs" line-rate="1" branch-rate="1" complexity="13">
          <methods>
            <method name="get_PublicKey" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="11" hits="180" branch="False" />
              </lines>
            </method>
            <method name="get_PrivateKey" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="16" hits="196" branch="False" />
              </lines>
            </method>
            <method name="get_KeySize" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="21" hits="99" branch="False" />
              </lines>
            </method>
            <method name="get_CreatedAt" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="26" hits="62" branch="False" />
              </lines>
            </method>
            <method name="get_Algorithm" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="31" hits="100" branch="False" />
              </lines>
            </method>
            <method name="IsValid" signature="()" line-rate="1" branch-rate="1" complexity="4">
              <lines>
                <line number="61" hits="29" branch="True" condition-coverage="100% (4/4)">
                  <conditions>
                    <condition number="11" type="jump" coverage="100%" />
                    <condition number="24" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="62" hits="29" branch="False" />
                <line number="63" hits="29" branch="False" />
              </lines>
            </method>
            <method name="ClearPrivateKey" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="71" hits="3" branch="False" />
                <line number="72" hits="3" branch="False" />
              </lines>
            </method>
            <method name="GetPublicKeyOnly" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="80" hits="3" branch="False" />
                <line number="81" hits="3" branch="False" />
                <line number="82" hits="3" branch="False" />
                <line number="83" hits="3" branch="False" />
                <line number="84" hits="3" branch="False" />
                <line number="85" hits="3" branch="False" />
                <line number="86" hits="3" branch="False" />
                <line number="87" hits="3" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="()" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="36" hits="18" branch="False" />
                <line number="38" hits="18" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String,System.Int32,System.String)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="47" hits="38" branch="False" />
                <line number="49" hits="38" branch="False" />
                <line number="50" hits="38" branch="False" />
                <line number="51" hits="38" branch="False" />
                <line number="52" hits="38" branch="False" />
                <line number="53" hits="38" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="11" hits="180" branch="False" />
            <line number="16" hits="196" branch="False" />
            <line number="21" hits="99" branch="False" />
            <line number="26" hits="62" branch="False" />
            <line number="31" hits="100" branch="False" />
            <line number="61" hits="29" branch="True" condition-coverage="100% (4/4)">
              <conditions>
                <condition number="11" type="jump" coverage="100%" />
                <condition number="24" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="62" hits="29" branch="False" />
            <line number="63" hits="29" branch="False" />
            <line number="71" hits="3" branch="False" />
            <line number="72" hits="3" branch="False" />
            <line number="80" hits="3" branch="False" />
            <line number="81" hits="3" branch="False" />
            <line number="82" hits="3" branch="False" />
            <line number="83" hits="3" branch="False" />
            <line number="84" hits="3" branch="False" />
            <line number="85" hits="3" branch="False" />
            <line number="86" hits="3" branch="False" />
            <line number="87" hits="3" branch="False" />
            <line number="36" hits="18" branch="False" />
            <line number="38" hits="18" branch="False" />
            <line number="47" hits="38" branch="False" />
            <line number="49" hits="38" branch="False" />
            <line number="50" hits="38" branch="False" />
            <line number="51" hits="38" branch="False" />
            <line number="52" hits="38" branch="False" />
            <line number="53" hits="38" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ByteArrayCryptoExtensions" filename="Extensions\ByteArrayCryptoExtensions.cs" line-rate="0.6315" branch-rate="0.5192" complexity="56">
          <methods>
            <method name="ToSha256Hash" signature="(System.Byte[])" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="18" hits="12" branch="False" />
                <line number="19" hits="12" branch="False" />
              </lines>
            </method>
            <method name="ToHexString" signature="(System.Byte[],System.Boolean)" line-rate="1" branch-rate="0.8332999999999999" complexity="6">
              <lines>
                <line number="42" hits="9" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="43" hits="1" branch="False" />
                <line number="45" hits="8" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="21" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="46" hits="1" branch="False" />
                <line number="48" hits="7" branch="False" />
                <line number="49" hits="7" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="37" type="jump" coverage="50%" />
                  </conditions>
                </line>
              </lines>
            </method>
            <method name="ToBase64String" signature="(System.Byte[])" line-rate="1" branch-rate="1" complexity="4">
              <lines>
                <line number="59" hits="11" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="60" hits="1" branch="False" />
                <line number="62" hits="10" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="21" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="63" hits="2" branch="False" />
                <line number="65" hits="8" branch="False" />
              </lines>
            </method>
            <method name="SecureClear" signature="(System.Byte[])" line-rate="1" branch-rate="1" complexity="4">
              <lines>
                <line number="74" hits="6" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="75" hits="1" branch="False" />
                <line number="77" hits="5" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="21" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="79" hits="4" branch="False" />
                <line number="81" hits="5" branch="False" />
              </lines>
            </method>
            <method name="SecureEquals" signature="(System.Byte[],System.Byte[])" line-rate="0" branch-rate="0" complexity="12">
              <lines>
                <line number="91" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="1" type="jump" coverage="0%" />
                    <condition number="4" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="92" hits="0" branch="False" />
                <line number="94" hits="0" branch="True" condition-coverage="0% (0/4)">
                  <conditions>
                    <condition number="9" type="jump" coverage="0%" />
                    <condition number="12" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="95" hits="0" branch="False" />
                <line number="97" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="22" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="98" hits="0" branch="False" />
                <line number="100" hits="0" branch="False" />
                <line number="101" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="50" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="103" hits="0" branch="False" />
                <line number="106" hits="0" branch="False" />
              </lines>
            </method>
            <method name="GenerateRandomBytes" signature="(System.Int32)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="116" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="2" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="117" hits="0" branch="False" />
                <line number="119" hits="0" branch="False" />
                <line number="120" hits="0" branch="False" />
                <line number="121" hits="0" branch="False" />
                <line number="122" hits="0" branch="False" />
                <line number="123" hits="0" branch="False" />
              </lines>
            </method>
            <method name="VerifySha256Hash" signature="(System.Byte[],System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="133" hits="0" branch="False" />
                <line number="134" hits="0" branch="False" />
              </lines>
            </method>
            <method name="EncryptAes" signature="(System.Byte[],System.Byte[],System.Byte[])" line-rate="1" branch-rate="1" complexity="8">
              <lines>
                <line number="146" hits="14" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="147" hits="1" branch="False" />
                <line number="148" hits="13" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="20" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="149" hits="1" branch="False" />
                <line number="152" hits="12" branch="False" />
                <line number="153" hits="12" branch="False" />
                <line number="155" hits="12" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="52" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="157" hits="1" branch="False" />
                <line number="160" hits="12" branch="False" />
                <line number="161" hits="12" branch="False" />
                <line number="162" hits="12" branch="False" />
                <line number="164" hits="12" branch="False" />
                <line number="165" hits="12" branch="False" />
                <line number="167" hits="12" branch="False" />
                <line number="170" hits="12" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="109" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="172" hits="11" branch="False" />
                <line number="173" hits="11" branch="False" />
                <line number="174" hits="11" branch="False" />
                <line number="175" hits="11" branch="False" />
                <line number="180" hits="1" branch="False" />
                <line number="182" hits="12" branch="False" />
              </lines>
            </method>
            <method name="DecryptAes" signature="(System.Byte[],System.Byte[],System.Byte[])" line-rate="0.84" branch-rate="0.6" complexity="10">
              <lines>
                <line number="208" hits="11" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="1" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="209" hits="0" branch="False" />
                <line number="210" hits="11" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="20" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="211" hits="0" branch="False" />
                <line number="214" hits="11" branch="False" />
                <line number="215" hits="11" branch="False" />
                <line number="220" hits="11" branch="True" condition-coverage="100% (2/2)">
                  <conditions>
                    <condition number="52" type="jump" coverage="100%" />
                  </conditions>
                </line>
                <line number="223" hits="1" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="59" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="224" hits="0" branch="False" />
                <line number="225" hits="1" branch="False" />
                <line number="226" hits="1" branch="False" />
                <line number="231" hits="10" branch="True" condition-coverage="50% (1/2)">
                  <conditions>
                    <condition number="88" type="jump" coverage="50%" />
                  </conditions>
                </line>
                <line number="232" hits="0" branch="False" />
                <line number="234" hits="10" branch="False" />
                <line number="235" hits="10" branch="False" />
                <line number="237" hits="10" branch="False" />
                <line number="238" hits="10" branch="False" />
                <line number="241" hits="11" branch="False" />
                <line number="243" hits="11" branch="False" />
                <line number="244" hits="11" branch="False" />
                <line number="245" hits="11" branch="False" />
                <line number="246" hits="11" branch="False" />
                <line number="248" hits="11" branch="False" />
                <line number="249" hits="11" branch="False" />
                <line number="250" hits="11" branch="False" />
              </lines>
            </method>
            <method name="SignRsa" signature="(System.Byte[],System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="275" hits="0" branch="False" />
                <line number="276" hits="0" branch="False" />
              </lines>
            </method>
            <method name="VerifyRsaSignature" signature="(System.Byte[],System.Byte[],System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="301" hits="0" branch="False" />
                <line number="302" hits="0" branch="False" />
              </lines>
            </method>
            <method name="FromHexString" signature="(System.String)" line-rate="0" branch-rate="0" complexity="4">
              <lines>
                <line number="326" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="6" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="327" hits="0" branch="False" />
                <line number="329" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="22" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="330" hits="0" branch="False" />
                <line number="332" hits="0" branch="False" />
              </lines>
            </method>
            <method name="FromBase64String" signature="(System.String)" line-rate="0" branch-rate="0" complexity="2">
              <lines>
                <line number="342" hits="0" branch="True" condition-coverage="0% (0/2)">
                  <conditions>
                    <condition number="6" type="jump" coverage="0%" />
                  </conditions>
                </line>
                <line number="343" hits="0" branch="False" />
                <line number="345" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="18" hits="12" branch="False" />
            <line number="19" hits="12" branch="False" />
            <line number="42" hits="9" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="43" hits="1" branch="False" />
            <line number="45" hits="8" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="21" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="46" hits="1" branch="False" />
            <line number="48" hits="7" branch="False" />
            <line number="49" hits="7" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="37" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="59" hits="11" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="60" hits="1" branch="False" />
            <line number="62" hits="10" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="21" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="63" hits="2" branch="False" />
            <line number="65" hits="8" branch="False" />
            <line number="74" hits="6" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="75" hits="1" branch="False" />
            <line number="77" hits="5" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="21" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="79" hits="4" branch="False" />
            <line number="81" hits="5" branch="False" />
            <line number="91" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="1" type="jump" coverage="0%" />
                <condition number="4" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="92" hits="0" branch="False" />
            <line number="94" hits="0" branch="True" condition-coverage="0% (0/4)">
              <conditions>
                <condition number="9" type="jump" coverage="0%" />
                <condition number="12" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="95" hits="0" branch="False" />
            <line number="97" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="22" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="98" hits="0" branch="False" />
            <line number="100" hits="0" branch="False" />
            <line number="101" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="50" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="103" hits="0" branch="False" />
            <line number="106" hits="0" branch="False" />
            <line number="116" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="2" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="117" hits="0" branch="False" />
            <line number="119" hits="0" branch="False" />
            <line number="120" hits="0" branch="False" />
            <line number="121" hits="0" branch="False" />
            <line number="122" hits="0" branch="False" />
            <line number="123" hits="0" branch="False" />
            <line number="133" hits="0" branch="False" />
            <line number="134" hits="0" branch="False" />
            <line number="146" hits="14" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="1" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="147" hits="1" branch="False" />
            <line number="148" hits="13" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="20" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="149" hits="1" branch="False" />
            <line number="152" hits="12" branch="False" />
            <line number="153" hits="12" branch="False" />
            <line number="155" hits="12" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="52" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="157" hits="1" branch="False" />
            <line number="160" hits="12" branch="False" />
            <line number="161" hits="12" branch="False" />
            <line number="162" hits="12" branch="False" />
            <line number="164" hits="12" branch="False" />
            <line number="165" hits="12" branch="False" />
            <line number="167" hits="12" branch="False" />
            <line number="170" hits="12" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="109" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="172" hits="11" branch="False" />
            <line number="173" hits="11" branch="False" />
            <line number="174" hits="11" branch="False" />
            <line number="175" hits="11" branch="False" />
            <line number="180" hits="1" branch="False" />
            <line number="182" hits="12" branch="False" />
            <line number="208" hits="11" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="1" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="209" hits="0" branch="False" />
            <line number="210" hits="11" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="20" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="211" hits="0" branch="False" />
            <line number="214" hits="11" branch="False" />
            <line number="215" hits="11" branch="False" />
            <line number="220" hits="11" branch="True" condition-coverage="100% (2/2)">
              <conditions>
                <condition number="52" type="jump" coverage="100%" />
              </conditions>
            </line>
            <line number="223" hits="1" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="59" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="224" hits="0" branch="False" />
            <line number="225" hits="1" branch="False" />
            <line number="226" hits="1" branch="False" />
            <line number="231" hits="10" branch="True" condition-coverage="50% (1/2)">
              <conditions>
                <condition number="88" type="jump" coverage="50%" />
              </conditions>
            </line>
            <line number="232" hits="0" branch="False" />
            <line number="234" hits="10" branch="False" />
            <line number="235" hits="10" branch="False" />
            <line number="237" hits="10" branch="False" />
            <line number="238" hits="10" branch="False" />
            <line number="241" hits="11" branch="False" />
            <line number="243" hits="11" branch="False" />
            <line number="244" hits="11" branch="False" />
            <line number="245" hits="11" branch="False" />
            <line number="246" hits="11" branch="False" />
            <line number="248" hits="11" branch="False" />
            <line number="249" hits="11" branch="False" />
            <line number="250" hits="11" branch="False" />
            <line number="275" hits="0" branch="False" />
            <line number="276" hits="0" branch="False" />
            <line number="301" hits="0" branch="False" />
            <line number="302" hits="0" branch="False" />
            <line number="326" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="6" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="327" hits="0" branch="False" />
            <line number="329" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="22" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="330" hits="0" branch="False" />
            <line number="332" hits="0" branch="False" />
            <line number="342" hits="0" branch="True" condition-coverage="0% (0/2)">
              <conditions>
                <condition number="6" type="jump" coverage="0%" />
              </conditions>
            </line>
            <line number="343" hits="0" branch="False" />
            <line number="345" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ByteArrayCryptoExtensions/&lt;DecryptAesAsync&gt;d__11" filename="Extensions\ByteArrayCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="262" hits="0" branch="False" />
                <line number="263" hits="0" branch="False" />
                <line number="264" hits="0" branch="False" />
                <line number="265" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="262" hits="0" branch="False" />
            <line number="263" hits="0" branch="False" />
            <line number="264" hits="0" branch="False" />
            <line number="265" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ByteArrayCryptoExtensions/&lt;EncryptAesAsync&gt;d__9" filename="Extensions\ByteArrayCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="194" hits="0" branch="False" />
                <line number="195" hits="0" branch="False" />
                <line number="196" hits="0" branch="False" />
                <line number="197" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="194" hits="0" branch="False" />
            <line number="195" hits="0" branch="False" />
            <line number="196" hits="0" branch="False" />
            <line number="197" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ByteArrayCryptoExtensions/&lt;SignRsaAsync&gt;d__13" filename="Extensions\ByteArrayCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="288" hits="0" branch="False" />
                <line number="289" hits="0" branch="False" />
                <line number="290" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="288" hits="0" branch="False" />
            <line number="289" hits="0" branch="False" />
            <line number="290" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ByteArrayCryptoExtensions/&lt;ToSha256HashAsync&gt;d__1" filename="Extensions\ByteArrayCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="30" hits="0" branch="False" />
                <line number="31" hits="0" branch="False" />
                <line number="32" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="30" hits="0" branch="False" />
            <line number="31" hits="0" branch="False" />
            <line number="32" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ByteArrayCryptoExtensions/&lt;VerifyRsaSignatureAsync&gt;d__15" filename="Extensions\ByteArrayCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="315" hits="0" branch="False" />
                <line number="316" hits="0" branch="False" />
                <line number="317" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="315" hits="0" branch="False" />
            <line number="316" hits="0" branch="False" />
            <line number="317" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ModernCryptoExtensions" filename="Extensions\ModernCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="6">
          <methods>
            <method name="EncryptChaCha20Poly1305" signature="(System.String,System.Byte[],System.Byte[])" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="20" hits="0" branch="False" />
                <line number="21" hits="0" branch="False" />
                <line number="22" hits="0" branch="False" />
                <line number="23" hits="0" branch="False" />
                <line number="24" hits="0" branch="False" />
              </lines>
            </method>
            <method name="DecryptChaCha20Poly1305" signature="(System.String,System.Byte[],System.Byte[])" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="55" hits="0" branch="False" />
                <line number="56" hits="0" branch="False" />
                <line number="57" hits="0" branch="False" />
                <line number="58" hits="0" branch="False" />
                <line number="59" hits="0" branch="False" />
              </lines>
            </method>
            <method name="SignEcdsa" signature="(System.String,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="89" hits="0" branch="False" />
                <line number="90" hits="0" branch="False" />
                <line number="91" hits="0" branch="False" />
                <line number="92" hits="0" branch="False" />
                <line number="93" hits="0" branch="False" />
              </lines>
            </method>
            <method name="VerifyEcdsaSignature" signature="(System.String,System.String,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="123" hits="0" branch="False" />
                <line number="124" hits="0" branch="False" />
                <line number="125" hits="0" branch="False" />
                <line number="129" hits="0" branch="False" />
                <line number="130" hits="0" branch="False" />
                <line number="131" hits="0" branch="False" />
                <line number="133" hits="0" branch="False" />
                <line number="136" hits="0" branch="False" />
                <line number="138" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ToArgon2Hash" signature="(System.String,Liam.Cryptography.Services.Argon2PasswordHasher/Argon2Options)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="168" hits="0" branch="False" />
                <line number="169" hits="0" branch="False" />
                <line number="170" hits="0" branch="False" />
              </lines>
            </method>
            <method name="VerifyArgon2Hash" signature="(System.String,Liam.Cryptography.Services.Argon2PasswordHasher/Argon2HashResult)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="197" hits="0" branch="False" />
                <line number="198" hits="0" branch="False" />
                <line number="199" hits="0" branch="False" />
                <line number="200" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="20" hits="0" branch="False" />
            <line number="21" hits="0" branch="False" />
            <line number="22" hits="0" branch="False" />
            <line number="23" hits="0" branch="False" />
            <line number="24" hits="0" branch="False" />
            <line number="55" hits="0" branch="False" />
            <line number="56" hits="0" branch="False" />
            <line number="57" hits="0" branch="False" />
            <line number="58" hits="0" branch="False" />
            <line number="59" hits="0" branch="False" />
            <line number="89" hits="0" branch="False" />
            <line number="90" hits="0" branch="False" />
            <line number="91" hits="0" branch="False" />
            <line number="92" hits="0" branch="False" />
            <line number="93" hits="0" branch="False" />
            <line number="123" hits="0" branch="False" />
            <line number="124" hits="0" branch="False" />
            <line number="125" hits="0" branch="False" />
            <line number="129" hits="0" branch="False" />
            <line number="130" hits="0" branch="False" />
            <line number="131" hits="0" branch="False" />
            <line number="133" hits="0" branch="False" />
            <line number="136" hits="0" branch="False" />
            <line number="138" hits="0" branch="False" />
            <line number="168" hits="0" branch="False" />
            <line number="169" hits="0" branch="False" />
            <line number="170" hits="0" branch="False" />
            <line number="197" hits="0" branch="False" />
            <line number="198" hits="0" branch="False" />
            <line number="199" hits="0" branch="False" />
            <line number="200" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ModernCryptoExtensions/&lt;DecryptChaCha20Poly1305Async&gt;d__3" filename="Extensions\ModernCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="73" hits="0" branch="False" />
                <line number="74" hits="0" branch="False" />
                <line number="75" hits="0" branch="False" />
                <line number="76" hits="0" branch="False" />
                <line number="77" hits="0" branch="False" />
                <line number="78" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="73" hits="0" branch="False" />
            <line number="74" hits="0" branch="False" />
            <line number="75" hits="0" branch="False" />
            <line number="76" hits="0" branch="False" />
            <line number="77" hits="0" branch="False" />
            <line number="78" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ModernCryptoExtensions/&lt;EncryptChaCha20Poly1305Async&gt;d__1" filename="Extensions\ModernCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="38" hits="0" branch="False" />
                <line number="39" hits="0" branch="False" />
                <line number="40" hits="0" branch="False" />
                <line number="41" hits="0" branch="False" />
                <line number="42" hits="0" branch="False" />
                <line number="43" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="38" hits="0" branch="False" />
            <line number="39" hits="0" branch="False" />
            <line number="40" hits="0" branch="False" />
            <line number="41" hits="0" branch="False" />
            <line number="42" hits="0" branch="False" />
            <line number="43" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ModernCryptoExtensions/&lt;SignEcdsaAsync&gt;d__5" filename="Extensions\ModernCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="106" hits="0" branch="False" />
                <line number="107" hits="0" branch="False" />
                <line number="108" hits="0" branch="False" />
                <line number="109" hits="0" branch="False" />
                <line number="110" hits="0" branch="False" />
                <line number="111" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="106" hits="0" branch="False" />
            <line number="107" hits="0" branch="False" />
            <line number="108" hits="0" branch="False" />
            <line number="109" hits="0" branch="False" />
            <line number="110" hits="0" branch="False" />
            <line number="111" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ModernCryptoExtensions/&lt;ToArgon2HashAsync&gt;d__9" filename="Extensions\ModernCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="183" hits="0" branch="False" />
                <line number="184" hits="0" branch="False" />
                <line number="185" hits="0" branch="False" />
                <line number="186" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="183" hits="0" branch="False" />
            <line number="184" hits="0" branch="False" />
            <line number="185" hits="0" branch="False" />
            <line number="186" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ModernCryptoExtensions/&lt;VerifyArgon2HashAsync&gt;d__11" filename="Extensions\ModernCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="213" hits="0" branch="False" />
                <line number="214" hits="0" branch="False" />
                <line number="215" hits="0" branch="False" />
                <line number="216" hits="0" branch="False" />
                <line number="217" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="213" hits="0" branch="False" />
            <line number="214" hits="0" branch="False" />
            <line number="215" hits="0" branch="False" />
            <line number="216" hits="0" branch="False" />
            <line number="217" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.ModernCryptoExtensions/&lt;VerifyEcdsaSignatureAsync&gt;d__7" filename="Extensions\ModernCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="151" hits="0" branch="False" />
                <line number="152" hits="0" branch="False" />
                <line number="153" hits="0" branch="False" />
                <line number="154" hits="0" branch="False" />
                <line number="155" hits="0" branch="False" />
                <line number="156" hits="0" branch="False" />
                <line number="157" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="151" hits="0" branch="False" />
            <line number="152" hits="0" branch="False" />
            <line number="153" hits="0" branch="False" />
            <line number="154" hits="0" branch="False" />
            <line number="155" hits="0" branch="False" />
            <line number="156" hits="0" branch="False" />
            <line number="157" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StreamCryptoExtensions" filename="Extensions\StreamCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="6">
          <methods>
            <method name="EncryptAesStream" signature="(System.IO.Stream,System.IO.Stream,System.Byte[],System.Byte[],System.Int32)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="21" hits="0" branch="False" />
                <line number="22" hits="0" branch="False" />
                <line number="23" hits="0" branch="False" />
                <line number="25" hits="0" branch="False" />
                <line number="26" hits="0" branch="False" />
                <line number="27" hits="0" branch="False" />
              </lines>
            </method>
            <method name="DecryptAesStream" signature="(System.IO.Stream,System.IO.Stream,System.Byte[],System.Byte[],System.Int32)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="60" hits="0" branch="False" />
                <line number="61" hits="0" branch="False" />
                <line number="62" hits="0" branch="False" />
                <line number="64" hits="0" branch="False" />
                <line number="65" hits="0" branch="False" />
                <line number="66" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ComputeSha256Hash" signature="(System.IO.Stream,System.Int32)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="97" hits="0" branch="False" />
                <line number="99" hits="0" branch="False" />
                <line number="100" hits="0" branch="False" />
              </lines>
            </method>
            <method name="EncryptFile" signature="(System.String,System.String,System.Byte[],System.Byte[],System.Int32)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="130" hits="0" branch="False" />
                <line number="131" hits="0" branch="False" />
                <line number="132" hits="0" branch="False" />
                <line number="134" hits="0" branch="False" />
                <line number="135" hits="0" branch="False" />
                <line number="137" hits="0" branch="False" />
                <line number="138" hits="0" branch="False" />
              </lines>
            </method>
            <method name="DecryptFile" signature="(System.String,System.String,System.Byte[],System.Byte[],System.Int32)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="173" hits="0" branch="False" />
                <line number="174" hits="0" branch="False" />
                <line number="175" hits="0" branch="False" />
                <line number="177" hits="0" branch="False" />
                <line number="178" hits="0" branch="False" />
                <line number="180" hits="0" branch="False" />
                <line number="181" hits="0" branch="False" />
              </lines>
            </method>
            <method name="ComputeFileSha256Hash" signature="(System.String,System.Int32)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="214" hits="0" branch="False" />
                <line number="216" hits="0" branch="False" />
                <line number="217" hits="0" branch="False" />
                <line number="218" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="21" hits="0" branch="False" />
            <line number="22" hits="0" branch="False" />
            <line number="23" hits="0" branch="False" />
            <line number="25" hits="0" branch="False" />
            <line number="26" hits="0" branch="False" />
            <line number="27" hits="0" branch="False" />
            <line number="60" hits="0" branch="False" />
            <line number="61" hits="0" branch="False" />
            <line number="62" hits="0" branch="False" />
            <line number="64" hits="0" branch="False" />
            <line number="65" hits="0" branch="False" />
            <line number="66" hits="0" branch="False" />
            <line number="97" hits="0" branch="False" />
            <line number="99" hits="0" branch="False" />
            <line number="100" hits="0" branch="False" />
            <line number="130" hits="0" branch="False" />
            <line number="131" hits="0" branch="False" />
            <line number="132" hits="0" branch="False" />
            <line number="134" hits="0" branch="False" />
            <line number="135" hits="0" branch="False" />
            <line number="137" hits="0" branch="False" />
            <line number="138" hits="0" branch="False" />
            <line number="173" hits="0" branch="False" />
            <line number="174" hits="0" branch="False" />
            <line number="175" hits="0" branch="False" />
            <line number="177" hits="0" branch="False" />
            <line number="178" hits="0" branch="False" />
            <line number="180" hits="0" branch="False" />
            <line number="181" hits="0" branch="False" />
            <line number="214" hits="0" branch="False" />
            <line number="216" hits="0" branch="False" />
            <line number="217" hits="0" branch="False" />
            <line number="218" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StreamCryptoExtensions/&lt;ComputeFileSha256HashAsync&gt;d__11" filename="Extensions\StreamCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="230" hits="0" branch="False" />
                <line number="232" hits="0" branch="False" />
                <line number="233" hits="0" branch="False" />
                <line number="234" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="230" hits="0" branch="False" />
            <line number="232" hits="0" branch="False" />
            <line number="233" hits="0" branch="False" />
            <line number="234" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StreamCryptoExtensions/&lt;ComputeSha256HashAsync&gt;d__5" filename="Extensions\StreamCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="113" hits="0" branch="False" />
                <line number="115" hits="0" branch="False" />
                <line number="116" hits="0" branch="False" />
                <line number="117" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="113" hits="0" branch="False" />
            <line number="115" hits="0" branch="False" />
            <line number="116" hits="0" branch="False" />
            <line number="117" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StreamCryptoExtensions/&lt;DecryptAesStreamAsync&gt;d__3" filename="Extensions\StreamCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="80" hits="0" branch="False" />
                <line number="81" hits="0" branch="False" />
                <line number="82" hits="0" branch="False" />
                <line number="84" hits="0" branch="False" />
                <line number="85" hits="0" branch="False" />
                <line number="86" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="80" hits="0" branch="False" />
            <line number="81" hits="0" branch="False" />
            <line number="82" hits="0" branch="False" />
            <line number="84" hits="0" branch="False" />
            <line number="85" hits="0" branch="False" />
            <line number="86" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StreamCryptoExtensions/&lt;DecryptFileAsync&gt;d__9" filename="Extensions\StreamCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="195" hits="0" branch="False" />
                <line number="196" hits="0" branch="False" />
                <line number="197" hits="0" branch="False" />
                <line number="199" hits="0" branch="False" />
                <line number="200" hits="0" branch="False" />
                <line number="202" hits="0" branch="False" />
                <line number="203" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="195" hits="0" branch="False" />
            <line number="196" hits="0" branch="False" />
            <line number="197" hits="0" branch="False" />
            <line number="199" hits="0" branch="False" />
            <line number="200" hits="0" branch="False" />
            <line number="202" hits="0" branch="False" />
            <line number="203" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StreamCryptoExtensions/&lt;EncryptAesStreamAsync&gt;d__1" filename="Extensions\StreamCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="41" hits="0" branch="False" />
                <line number="42" hits="0" branch="False" />
                <line number="43" hits="0" branch="False" />
                <line number="45" hits="0" branch="False" />
                <line number="46" hits="0" branch="False" />
                <line number="47" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="41" hits="0" branch="False" />
            <line number="42" hits="0" branch="False" />
            <line number="43" hits="0" branch="False" />
            <line number="45" hits="0" branch="False" />
            <line number="46" hits="0" branch="False" />
            <line number="47" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StreamCryptoExtensions/&lt;EncryptFileAsync&gt;d__7" filename="Extensions\StreamCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="152" hits="0" branch="False" />
                <line number="153" hits="0" branch="False" />
                <line number="154" hits="0" branch="False" />
                <line number="156" hits="0" branch="False" />
                <line number="157" hits="0" branch="False" />
                <line number="159" hits="0" branch="False" />
                <line number="160" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="152" hits="0" branch="False" />
            <line number="153" hits="0" branch="False" />
            <line number="154" hits="0" branch="False" />
            <line number="156" hits="0" branch="False" />
            <line number="157" hits="0" branch="False" />
            <line number="159" hits="0" branch="False" />
            <line number="160" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StringCryptoExtensions" filename="Extensions\StringCryptoExtensions.cs" line-rate="0.8536" branch-rate="1" complexity="8">
          <methods>
            <method name="ToSha256Hash" signature="(System.String)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="19" hits="14" branch="False" />
                <line number="20" hits="13" branch="False" />
                <line number="21" hits="13" branch="False" />
              </lines>
            </method>
            <method name="EncryptAes" signature="(System.String,System.Byte[],System.Byte[])" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="48" hits="11" branch="False" />
                <line number="49" hits="10" branch="False" />
                <line number="50" hits="9" branch="False" />
                <line number="51" hits="9" branch="False" />
                <line number="52" hits="9" branch="False" />
              </lines>
            </method>
            <method name="DecryptAes" signature="(System.String,System.Byte[],System.Byte[])" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="83" hits="8" branch="False" />
                <line number="84" hits="8" branch="False" />
                <line number="85" hits="8" branch="False" />
                <line number="86" hits="8" branch="False" />
                <line number="87" hits="8" branch="False" />
              </lines>
            </method>
            <method name="EncryptRsa" signature="(System.String,System.String)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="117" hits="11" branch="False" />
                <line number="118" hits="10" branch="False" />
                <line number="119" hits="9" branch="False" />
                <line number="120" hits="9" branch="False" />
                <line number="121" hits="8" branch="False" />
              </lines>
            </method>
            <method name="DecryptRsa" signature="(System.String,System.String)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="150" hits="7" branch="False" />
                <line number="151" hits="7" branch="False" />
                <line number="152" hits="7" branch="False" />
                <line number="153" hits="7" branch="False" />
                <line number="154" hits="7" branch="False" />
              </lines>
            </method>
            <method name="SignRsa" signature="(System.String,System.String)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="183" hits="10" branch="False" />
                <line number="184" hits="9" branch="False" />
                <line number="185" hits="9" branch="False" />
                <line number="186" hits="9" branch="False" />
                <line number="187" hits="9" branch="False" />
              </lines>
            </method>
            <method name="VerifyRsaSignature" signature="(System.String,System.String,System.String)" line-rate="0.7777" branch-rate="1" complexity="1">
              <lines>
                <line number="217" hits="9" branch="False" />
                <line number="218" hits="8" branch="False" />
                <line number="219" hits="8" branch="False" />
                <line number="223" hits="8" branch="False" />
                <line number="224" hits="8" branch="False" />
                <line number="225" hits="8" branch="False" />
                <line number="227" hits="0" branch="False" />
                <line number="230" hits="0" branch="False" />
                <line number="232" hits="8" branch="False" />
              </lines>
            </method>
            <method name="VerifySha256Hash" signature="(System.String,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="262" hits="0" branch="False" />
                <line number="263" hits="0" branch="False" />
                <line number="264" hits="0" branch="False" />
                <line number="265" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="19" hits="14" branch="False" />
            <line number="20" hits="13" branch="False" />
            <line number="21" hits="13" branch="False" />
            <line number="48" hits="11" branch="False" />
            <line number="49" hits="10" branch="False" />
            <line number="50" hits="9" branch="False" />
            <line number="51" hits="9" branch="False" />
            <line number="52" hits="9" branch="False" />
            <line number="83" hits="8" branch="False" />
            <line number="84" hits="8" branch="False" />
            <line number="85" hits="8" branch="False" />
            <line number="86" hits="8" branch="False" />
            <line number="87" hits="8" branch="False" />
            <line number="117" hits="11" branch="False" />
            <line number="118" hits="10" branch="False" />
            <line number="119" hits="9" branch="False" />
            <line number="120" hits="9" branch="False" />
            <line number="121" hits="8" branch="False" />
            <line number="150" hits="7" branch="False" />
            <line number="151" hits="7" branch="False" />
            <line number="152" hits="7" branch="False" />
            <line number="153" hits="7" branch="False" />
            <line number="154" hits="7" branch="False" />
            <line number="183" hits="10" branch="False" />
            <line number="184" hits="9" branch="False" />
            <line number="185" hits="9" branch="False" />
            <line number="186" hits="9" branch="False" />
            <line number="187" hits="9" branch="False" />
            <line number="217" hits="9" branch="False" />
            <line number="218" hits="8" branch="False" />
            <line number="219" hits="8" branch="False" />
            <line number="223" hits="8" branch="False" />
            <line number="224" hits="8" branch="False" />
            <line number="225" hits="8" branch="False" />
            <line number="227" hits="0" branch="False" />
            <line number="230" hits="0" branch="False" />
            <line number="232" hits="8" branch="False" />
            <line number="262" hits="0" branch="False" />
            <line number="263" hits="0" branch="False" />
            <line number="264" hits="0" branch="False" />
            <line number="265" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StringCryptoExtensions/&lt;DecryptAesAsync&gt;d__5" filename="Extensions\StringCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="101" hits="0" branch="False" />
                <line number="102" hits="0" branch="False" />
                <line number="103" hits="0" branch="False" />
                <line number="104" hits="0" branch="False" />
                <line number="105" hits="0" branch="False" />
                <line number="106" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="101" hits="0" branch="False" />
            <line number="102" hits="0" branch="False" />
            <line number="103" hits="0" branch="False" />
            <line number="104" hits="0" branch="False" />
            <line number="105" hits="0" branch="False" />
            <line number="106" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StringCryptoExtensions/&lt;DecryptRsaAsync&gt;d__9" filename="Extensions\StringCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="167" hits="0" branch="False" />
                <line number="168" hits="0" branch="False" />
                <line number="169" hits="0" branch="False" />
                <line number="170" hits="0" branch="False" />
                <line number="171" hits="0" branch="False" />
                <line number="172" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="167" hits="0" branch="False" />
            <line number="168" hits="0" branch="False" />
            <line number="169" hits="0" branch="False" />
            <line number="170" hits="0" branch="False" />
            <line number="171" hits="0" branch="False" />
            <line number="172" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StringCryptoExtensions/&lt;EncryptAesAsync&gt;d__3" filename="Extensions\StringCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="66" hits="0" branch="False" />
                <line number="67" hits="0" branch="False" />
                <line number="68" hits="0" branch="False" />
                <line number="69" hits="0" branch="False" />
                <line number="70" hits="0" branch="False" />
                <line number="71" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="66" hits="0" branch="False" />
            <line number="67" hits="0" branch="False" />
            <line number="68" hits="0" branch="False" />
            <line number="69" hits="0" branch="False" />
            <line number="70" hits="0" branch="False" />
            <line number="71" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StringCryptoExtensions/&lt;EncryptRsaAsync&gt;d__7" filename="Extensions\StringCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="134" hits="0" branch="False" />
                <line number="135" hits="0" branch="False" />
                <line number="136" hits="0" branch="False" />
                <line number="137" hits="0" branch="False" />
                <line number="138" hits="0" branch="False" />
                <line number="139" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="134" hits="0" branch="False" />
            <line number="135" hits="0" branch="False" />
            <line number="136" hits="0" branch="False" />
            <line number="137" hits="0" branch="False" />
            <line number="138" hits="0" branch="False" />
            <line number="139" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StringCryptoExtensions/&lt;SignRsaAsync&gt;d__11" filename="Extensions\StringCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="200" hits="0" branch="False" />
                <line number="201" hits="0" branch="False" />
                <line number="202" hits="0" branch="False" />
                <line number="203" hits="0" branch="False" />
                <line number="204" hits="0" branch="False" />
                <line number="205" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="200" hits="0" branch="False" />
            <line number="201" hits="0" branch="False" />
            <line number="202" hits="0" branch="False" />
            <line number="203" hits="0" branch="False" />
            <line number="204" hits="0" branch="False" />
            <line number="205" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StringCryptoExtensions/&lt;ToSha256HashAsync&gt;d__1" filename="Extensions\StringCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="33" hits="0" branch="False" />
                <line number="34" hits="0" branch="False" />
                <line number="35" hits="0" branch="False" />
                <line number="36" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="33" hits="0" branch="False" />
            <line number="34" hits="0" branch="False" />
            <line number="35" hits="0" branch="False" />
            <line number="36" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Extensions.StringCryptoExtensions/&lt;VerifyRsaSignatureAsync&gt;d__13" filename="Extensions\StringCryptoExtensions.cs" line-rate="0" branch-rate="1" complexity="1">
          <methods>
            <method name="MoveNext" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="245" hits="0" branch="False" />
                <line number="246" hits="0" branch="False" />
                <line number="247" hits="0" branch="False" />
                <line number="248" hits="0" branch="False" />
                <line number="249" hits="0" branch="False" />
                <line number="250" hits="0" branch="False" />
                <line number="251" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="245" hits="0" branch="False" />
            <line number="246" hits="0" branch="False" />
            <line number="247" hits="0" branch="False" />
            <line number="248" hits="0" branch="False" />
            <line number="249" hits="0" branch="False" />
            <line number="250" hits="0" branch="False" />
            <line number="251" hits="0" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Exceptions.CryptographyException" filename="Exceptions\CryptographyException.cs" line-rate="0.1875" branch-rate="1" complexity="6">
          <methods>
            <method name="get_ErrorCode" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="11" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="16" hits="0" branch="False" />
                <line number="18" hits="0" branch="False" />
                <line number="19" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="25" hits="0" branch="False" />
                <line number="27" hits="0" branch="False" />
                <line number="28" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="35" hits="0" branch="False" />
                <line number="37" hits="0" branch="False" />
                <line number="38" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Exception)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="45" hits="0" branch="False" />
                <line number="47" hits="0" branch="False" />
                <line number="48" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.String,System.Exception)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="56" hits="15" branch="False" />
                <line number="58" hits="15" branch="False" />
                <line number="59" hits="15" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="11" hits="0" branch="False" />
            <line number="16" hits="0" branch="False" />
            <line number="18" hits="0" branch="False" />
            <line number="19" hits="0" branch="False" />
            <line number="25" hits="0" branch="False" />
            <line number="27" hits="0" branch="False" />
            <line number="28" hits="0" branch="False" />
            <line number="35" hits="0" branch="False" />
            <line number="37" hits="0" branch="False" />
            <line number="38" hits="0" branch="False" />
            <line number="45" hits="0" branch="False" />
            <line number="47" hits="0" branch="False" />
            <line number="48" hits="0" branch="False" />
            <line number="56" hits="15" branch="False" />
            <line number="58" hits="15" branch="False" />
            <line number="59" hits="15" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Exceptions.EncryptionException" filename="Exceptions\CryptographyException.cs" line-rate="0.3333" branch-rate="1" complexity="3">
          <methods>
            <method name=".ctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="70" hits="0" branch="False" />
                <line number="72" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="78" hits="0" branch="False" />
                <line number="80" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Exception)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="87" hits="4" branch="False" />
                <line number="89" hits="4" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="70" hits="0" branch="False" />
            <line number="72" hits="0" branch="False" />
            <line number="78" hits="0" branch="False" />
            <line number="80" hits="0" branch="False" />
            <line number="87" hits="4" branch="False" />
            <line number="89" hits="4" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Exceptions.DecryptionException" filename="Exceptions\CryptographyException.cs" line-rate="0.3333" branch-rate="1" complexity="3">
          <methods>
            <method name=".ctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="100" hits="0" branch="False" />
                <line number="102" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="108" hits="0" branch="False" />
                <line number="110" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Exception)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="117" hits="4" branch="False" />
                <line number="119" hits="4" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="100" hits="0" branch="False" />
            <line number="102" hits="0" branch="False" />
            <line number="108" hits="0" branch="False" />
            <line number="110" hits="0" branch="False" />
            <line number="117" hits="4" branch="False" />
            <line number="119" hits="4" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Exceptions.KeyManagementException" filename="Exceptions\CryptographyException.cs" line-rate="0.3333" branch-rate="1" complexity="3">
          <methods>
            <method name=".ctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="130" hits="0" branch="False" />
                <line number="132" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="138" hits="0" branch="False" />
                <line number="140" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Exception)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="147" hits="6" branch="False" />
                <line number="149" hits="6" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="130" hits="0" branch="False" />
            <line number="132" hits="0" branch="False" />
            <line number="138" hits="0" branch="False" />
            <line number="140" hits="0" branch="False" />
            <line number="147" hits="6" branch="False" />
            <line number="149" hits="6" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Exceptions.DigitalSignatureException" filename="Exceptions\CryptographyException.cs" line-rate="0.3333" branch-rate="1" complexity="3">
          <methods>
            <method name=".ctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="160" hits="0" branch="False" />
                <line number="162" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="168" hits="0" branch="False" />
                <line number="170" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Exception)" line-rate="1" branch-rate="1" complexity="1">
              <lines>
                <line number="177" hits="1" branch="False" />
                <line number="179" hits="1" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="160" hits="0" branch="False" />
            <line number="162" hits="0" branch="False" />
            <line number="168" hits="0" branch="False" />
            <line number="170" hits="0" branch="False" />
            <line number="177" hits="1" branch="False" />
            <line number="179" hits="1" branch="False" />
          </lines>
        </class>
        <class name="Liam.Cryptography.Exceptions.SignatureException" filename="Exceptions\CryptographyException.cs" line-rate="0" branch-rate="1" complexity="3">
          <methods>
            <method name=".ctor" signature="()" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="190" hits="0" branch="False" />
                <line number="192" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="198" hits="0" branch="False" />
                <line number="200" hits="0" branch="False" />
              </lines>
            </method>
            <method name=".ctor" signature="(System.String,System.Exception)" line-rate="0" branch-rate="1" complexity="1">
              <lines>
                <line number="207" hits="0" branch="False" />
                <line number="209" hits="0" branch="False" />
              </lines>
            </method>
          </methods>
          <lines>
            <line number="190" hits="0" branch="False" />
            <line number="192" hits="0" branch="False" />
            <line number="198" hits="0" branch="False" />
            <line number="200" hits="0" branch="False" />
            <line number="207" hits="0" branch="False" />
            <line number="209" hits="0" branch="False" />
          </lines>
        </class>
      </classes>
    </package>
  </packages>
</coverage>