//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("Liam")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("现代化日志记录功能库，支持多级日志、多种输出目标、结构化日志、异步记录、日志轮转等功能，完全兼容Microsoft.Extensions.Logging")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0+8e3fbb372e0dd611900e80bc3845a91692bd2f0e")]
[assembly: System.Reflection.AssemblyProductAttribute("Liam.Logging")]
[assembly: System.Reflection.AssemblyTitleAttribute("Liam.Logging")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://gitee.com/liam-gitee/liam")]

// 由 MSBuild WriteCodeFragment 类生成。

