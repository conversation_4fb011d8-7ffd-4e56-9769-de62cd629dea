{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Liam.Cryptography/1.1.0": {"dependencies": {"Konscious.Security.Cryptography.Argon2": "1.3.1"}, "runtime": {"Liam.Cryptography.dll": {}}}, "Konscious.Security.Cryptography.Argon2/1.3.1": {"dependencies": {"Konscious.Security.Cryptography.Blake2": "1.1.1", "System.Memory": "4.5.4"}, "runtime": {"lib/net8.0/Konscious.Security.Cryptography.Argon2.dll": {"assemblyVersion": "1.3.1.0", "fileVersion": "1.3.1.0"}}}, "Konscious.Security.Cryptography.Blake2/1.1.1": {"runtime": {"lib/net8.0/Konscious.Security.Cryptography.Blake2.dll": {"assemblyVersion": "1.1.1.0", "fileVersion": "1.1.1.0"}}}, "System.Memory/4.5.4": {}}}, "libraries": {"Liam.Cryptography/1.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "Konscious.Security.Cryptography.Argon2/1.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-T+OAGwzYYXftahpOxO7J4xA5K6urxwGnWQf3M+Jpi+76Azv/0T3M5SuN+h7/QvXuiqNw3ZEZ5QqVLI5ygDAylw==", "path": "konscious.security.cryptography.argon2/1.3.1", "hashPath": "konscious.security.cryptography.argon2.1.3.1.nupkg.sha512"}, "Konscious.Security.Cryptography.Blake2/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-odwOyzj/J/lHJZNwFWJGU/LRecBShupAJ2S8TQqZfhUe9niHzu/voBYK5wuVKsvSpzbfupKQYZguVyIk1sgOkQ==", "path": "konscious.security.cryptography.blake2/1.1.1", "hashPath": "konscious.security.cryptography.blake2.1.1.1.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}}}