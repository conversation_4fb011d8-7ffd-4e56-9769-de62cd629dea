﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Liam.Cryptography</id>
    <version>1.0.0</version>
    <authors><PERSON></authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <readme>README.md</readme>
    <description>Liam系列加密功能库，提供完整的对称加密、非对称加密、哈希算法、数字签名和密钥管理功能</description>
    <tags>cryptography encryption decryption aes rsa sha256 md5 digital-signature key-management liam</tags>
    <repository type="git" url="https://gitee.com/your-username/Liam" />
    <dependencies>
      <group targetFramework="net8.0" />
    </dependencies>
  </metadata>
  <files>
    <file src="D:\Project\00 Liam\src\Liam.Cryptography\bin\Debug\net8.0\Liam.Cryptography.dll" target="lib\net8.0\Liam.Cryptography.dll" />
    <file src="D:\Project\00 Liam\src\Liam.Cryptography\bin\Debug\net8.0\Liam.Cryptography.xml" target="lib\net8.0\Liam.Cryptography.xml" />
    <file src="D:\Project\00 Liam\src\Liam.Cryptography\README.md" target="\README.md" />
  </files>
</package>